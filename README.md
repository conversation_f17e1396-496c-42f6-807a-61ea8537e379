# Kubernetes Setup for Development and Staging Environments

This document provides step-by-step instructions for tagging and pushing Docker images to the **Azure Container Registry (ACR)** and configuring Kubernetes clusters for the development and staging environments.

---

## Prerequisites

Before proceeding, ensure the following tools and configurations are in place:

1. **Docker**: Ensure Docker is installed and running on your machine.  
   [Install Docker](https://docs.docker.com/get-docker/) if needed.

2. **Azure CLI**: Install the Azure CLI and log in to your Azure account.  
   [Install Azure CLI](https://learn.microsoft.com/en-us/cli/azure/install-azure-cli)

3. **Login to Azure**: Authenticate with Azure using the following command:

bash
az login

4. **Access to Azure Container Registry (ACR)**: Log in to the appropriate ACR:

   - **For Development**:
     bash
     az acr login --name developmentkuberegistry

   - **For Staging**:
     bash
     az acr login --name stagingkuberegistry

   - **For Production**:
     bash
     az acr login --name productionkuberegistry

---

## Development Environment

### Docker Build Command

Build the Docker images for the development environment using the following commands:
bash
docker build -f Dockerfile.db -t dev-cm-db .
docker build -f Dockerfile.api -t dev-cm-api .
docker build -f Dockerfile.keycloak -t dev-cm-keycloak .
docker build -f Dockerfile.retriever -t dev-cm-retriever .
docker build -f Dockerfile.superset -t dev-cm-superset .
docker build -f Dockerfile.scheduler -t dev-cm-scheduler .
docker build -f Dockerfile.react -t dev-cm-react .
bash
docker buildx build --platform linux/amd64 -f Dockerfile.db -t dev-cm-db .
docker buildx build --platform linux/amd64 -f Dockerfile.api -t dev-cm-api .
docker buildx build --platform linux/amd64 -f Dockerfile.keycloak -t dev-cm-keycloak .
docker buildx build --platform linux/amd64 -f Dockerfile.retriever -t dev-cm-retriever .
docker buildx build --platform linux/amd64 -f Dockerfile.superset -t dev-cm-superset .
docker buildx build --platform linux/amd64 -f Dockerfile.scheduler -t dev-cm-scheduler .
docker buildx build --platform linux/amd64 -f Dockerfile.react -t dev-cm-react .

### Docker Image Tagging

Tag your local Docker images to prepare them for pushing to the ACR at developmentkuberegistry.azurecr.io:
bash
docker tag dev-cm-db developmentkuberegistry.azurecr.io/cm/postgres:latest
docker tag dev-cm-api developmentkuberegistry.azurecr.io/cm/backend_api:latest
docker tag dev-cm-keycloak developmentkuberegistry.azurecr.io/cm/keycloak:latest
docker tag dev-cm-retriver developmentkuberegistry.azurecr.io/cm/retriver_api:latest
docker tag dev-cm-superset developmentkuberegistry.azurecr.io/cm/superset:latest
docker tag dev-cm-scheduler developmentkuberegistry.azurecr.io/cm/scheduler:latest
docker tag dev-cm-react developmentkuberegistry.azurecr.io/cm/react_frontend:latest

### Pushing Docker Images to ACR

Push the tagged images to the Azure Container Registry:
bash
docker push developmentkuberegistry.azurecr.io/cm/postgres:latest
docker push developmentkuberegistry.azurecr.io/cm/backend_api:latest
docker push developmentkuberegistry.azurecr.io/cm/keycloak:latest
docker push developmentkuberegistry.azurecr.io/cm/retriver_api:latest
docker push developmentkuberegistry.azurecr.io/cm/superset:latest
docker push developmentkuberegistry.azurecr.io/cm/scheduler:latest
docker push developmentkuberegistry.azurecr.io/cm/react_frontend:latest

### Configuring kubectl for AKS

To interact with the development Kubernetes cluster, use the following command to fetch and configure credentials:
bash
az aks get-credentials --resource-group development-group --name development-kube --overwrite-existing

---

## Staging Environment

### Docker Build Command

Build the Docker images for the development environment using the following commands:
bash
docker build -f Dockerfile.db -t staging-cm-db .
docker build -f Dockerfile.api -t staging-cm-api .
docker build -f Dockerfile.keycloak -t staging-cm-keycloak .
docker build -f Dockerfile.retriever -t staging-cm-retriver .
docker build -f Dockerfile.superset -t staging-cm-superset .
docker build -f Dockerfile.scheduler -t staging-cm-scheduler .
docker build -f Dockerfile.react -t staging-cm-react .

### Docker Image Tagging

Tag your local Docker images to prepare them for pushing to the ACR at stagingkuberegistry.azurecr.io:
bash
docker tag staging-cm-db stagingkuberegistry.azurecr.io/cm/postgres:v.0.2.4
docker tag staging-cm-api stagingkuberegistry.azurecr.io/cm/backend_api:v.0.7.6
docker tag staging-cm-keycloak stagingkuberegistry.azurecr.io/cm/keycloak:v.0.0.4
docker tag staging-cm-retriver stagingkuberegistry.azurecr.io/cm/retriver_api:v.0.2.2
docker tag staging-cm-superset stagingkuberegistry.azurecr.io/cm/superset:v.0.0.6
docker tag staging-cm-scheduler stagingkuberegistry.azurecr.io/cm/scheduler:v.0.4.0

### Pushing Docker Images to ACR

Push the tagged images to the Azure Container Registry:
bash
docker push stagingkuberegistry.azurecr.io/cm/postgres:v.0.2.4
docker push stagingkuberegistry.azurecr.io/cm/backend_api:v.0.7.6
docker push stagingkuberegistry.azurecr.io/cm/keycloak:v.0.0.4
docker push stagingkuberegistry.azurecr.io/cm/retriver_api:v.0.2.2
docker push stagingkuberegistry.azurecr.io/cm/superset:v.0.0.6
docker push stagingkuberegistry.azurecr.io/cm/scheduler:v.0.4.0

**Note**: The version v.0.0.1 will change with each deployment.

### Configuring kubectl for AKS

To interact with the staging Kubernetes cluster, use the following command to fetch and configure credentials:
bash
az aks get-credentials --resource-group Staging-group --name staging-kube --overwrite-existing

---

## Production Environment

### Docker Build Command

Build the Docker images for the development environment using the following commands:
bash
docker build -f Dockerfile.db -t prod-cm-db .
docker build -f Dockerfile.api -t prod-cm-api .
docker build -f Dockerfile.keycloak -t prod-cm-keycloak .
docker build -f Dockerfile.retriever -t prod-cm-retriver .
docker build -f Dockerfile.superset -t prod-cm-superset .
docker build -f Dockerfile.scheduler -t prod-cm-scheduler .

### Docker Image Tagging

Tag your local Docker images to prepare them for pushing to the ACR at productionkuberegistry.azurecr.io:
bash
docker tag prod-cm-db productionkuberegistry.azurecr.io/cm/postgres:v.0.0.1
docker tag prod-cm-api productionkuberegistry.azurecr.io/cm/backend_api:v.0.0.6
docker tag prod-cm-keycloak productionkuberegistry.azurecr.io/cm/keycloak:v.0.0.2
docker tag prod-cm-retriver productionkuberegistry.azurecr.io/cm/retriver_api:v.0.0.5
docker tag prod-cm-superset productionkuberegistry.azurecr.io/cm/superset:v.0.0.1
docker tag prod-cm-scheduler productionkuberegistry.azurecr.io/cm/scheduler:v.0.0.5

### Pushing Docker Images to ACR

Push the tagged images to the Azure Container Registry:
bash
docker push productionkuberegistry.azurecr.io/cm/postgres:v.0.0.1
docker push productionkuberegistry.azurecr.io/cm/backend_api:v.0.0.6
docker push productionkuberegistry.azurecr.io/cm/keycloak:v.0.0.2
docker push productionkuberegistry.azurecr.io/cm/retriver_api:v.0.0.5
docker push productionkuberegistry.azurecr.io/cm/superset:v.0.0.1
docker push productionkuberegistry.azurecr.io/cm/scheduler:v.0.0.5

**Note**: The version v.0.0.1 will change with each deployment.

### Configuring kubectl for AKS

To interact with the staging Kubernetes cluster, use the following command to fetch and configure credentials:
bash
az aks get-credentials --resource-group production-group --name production-kube --overwrite-existing

---

## Verify the Configuration

After running the above command, you can verify the active cluster context with the following:
bash
kubectl config get-contexts

## Kubernetes Deployment

### Apply Deployment Configuration

Use the following command to apply your Kubernetes deployment configuration from the deployment.yaml file:
bash
kubectl apply -f .\deployment.yaml

### Check Pod Status

To check the status of your pods after deploying, use the following command:
bash
kubectl get pods

This will display a list of your pods, showing their names, statuses, and other details. If the pods are not running as expected, use the following command to describe the pod for more detailed information:
bash
kubectl describe pod <pod-name>

You can also view the logs of a specific pod with:
bash
kubectl logs <pod-name>

## Verify the Configuration

After running the kubectl apply command, you can verify the active cluster context with:
bash
kubectl config get-contexts

## Staging release (mongodb)

Take the dump of the database in dev

To take the dump of the whole database
bash
mongodump --uri="mongo_url" --db="database_name --out=./"file_name"

To take the dump of the specific collection (there is no way to take multiple collection in a single command)
bash
mongodump --uri="mongo_url" --db="database_name" --collection="collection_name" --out=./"file_name"

## Take the dump of the staging mongodb before loading the dev into staging

## After taking the whole dump, need to take the dump of the specific collection

## 1. implanting_physicians

## 2. impl_phy_procedure_type

## 3. site

## 4. user

## 5. referring_provider

## 6. user_procedure_type_mapping

## 7. user_site_mapping

## 8. superset_creds

## Load the dump into staging after deleting the staging database

## restore command

bash
mongorestore --uri="mongo_url" --db="database_name" "file_path"

## file_path example : "/home/<USER>/2025-02-19/cormetrix/"

## after loading get the patient id's from the patient collection and delete those using the FastAPI, and delete the chat_history etc collection

## once this done, delete the specific collection and load the dump of the specific collection that have taken from he staging before deleting and loading the dev dump
