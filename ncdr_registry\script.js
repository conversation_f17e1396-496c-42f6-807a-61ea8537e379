// DOM Elements
const caseSelect = document.getElementById('caseSelect');
const resultDiv = document.getElementById('result');
const resultSection = document.getElementById('result-section');
const clearResultBtn = document.getElementById('clearResultBtn');
const loadingOverlay = document.getElementById('loading-overlay');
const loadingText = document.getElementById('loading-text');
const toast = document.getElementById('toast');
const patientInfo = document.getElementById('patient-info');

// Buttons
const loadPatientsBtn = document.getElementById('loadPatientsBtn');
const submitBtn = document.getElementById('submitBtn');
const checkStatusBtn = document.getElementById('checkStatusBtn');

// Store loaded cases for reference
let loadedCases = [];

// Utility Functions
function showLoading(message = 'Loading...') {
    loadingText.textContent = message;
    loadingOverlay.style.display = 'flex';
    setTimeout(() => {
        loadingOverlay.style.opacity = '1';
    }, 10);
}

function hideLoading() {
    loadingOverlay.style.opacity = '0';
    setTimeout(() => {
        loadingOverlay.style.display = 'none';
    }, 300);
}

function showToast(message, type = 'info') {
    toast.textContent = message;
    toast.className = `toast ${type}`;
    toast.classList.add('show');

    setTimeout(() => {
        toast.classList.remove('show');
    }, 4000);
}

function updateButtonStates() {
    const hasDateRange = document.getElementById('startDate').value && document.getElementById('endDate').value;
    const hasSelectedCase = caseSelect.value;

    caseSelect.disabled = !hasDateRange;
    submitBtn.disabled = !hasSelectedCase;
    checkStatusBtn.disabled = !hasSelectedCase;

    if (!hasDateRange) {
        // Clear and add default option
        caseSelect.innerHTML = '';
        const defaultOption = document.createElement('option');
        defaultOption.value = '';
        defaultOption.textContent = '-- Select Date Range First --';
        caseSelect.appendChild(defaultOption);
        hidePatientInfo();
    }
}

function createSubmissionHistoryTable(history) {
    const table = document.createElement('table');
    table.className = 'submission-history-table';
    
    // Create header
    const thead = document.createElement('thead');
    const headerRow = document.createElement('tr');
    
    const headers = ['Status', 'Submission ID', 'Date & Time', 'Result Summary'];
    headers.forEach(headerText => {
        const th = document.createElement('th');
        th.textContent = headerText;
        headerRow.appendChild(th);
    });
    
    thead.appendChild(headerRow);
    table.appendChild(thead);
    
    // Create body
    const tbody = document.createElement('tbody');
    
    if (!history || !Array.isArray(history) || history.length === 0) {
        const row = document.createElement('tr');
        const cell = document.createElement('td');
        cell.colSpan = 4;
        cell.style.textAlign = 'center';
        cell.style.color = '#666';
        cell.style.fontStyle = 'italic';
        cell.textContent = 'No submission history available';
        row.appendChild(cell);
        tbody.appendChild(row);
    } else {
        history.forEach((submission, index) => {
            const row = document.createElement('tr');
            row.className = 'submission-row';
            row.setAttribute('data-index', index);
            
            // Status cell
            const statusCell = document.createElement('td');
            const statusIcon = submission.status === 'Completed' ? '✅' : 
                              submission.status === 'Pending' ? '⏳' : 
                              submission.status === 'Failed' ? '❌' : '❓';
            statusCell.textContent = `${statusIcon} ${submission.status}`;
            row.appendChild(statusCell);
            
            // Submission ID cell
            const idCell = document.createElement('td');
            const codeElement = document.createElement('code');
            codeElement.textContent = submission.submission_id;
            idCell.appendChild(codeElement);
            row.appendChild(idCell);
            
            // Date cell
            const dateCell = document.createElement('td');
            const formattedDate = new Date(submission.timestamp).toLocaleString();
            dateCell.textContent = formattedDate;
            row.appendChild(dateCell);
            
            // Status summary cell
            const summaryCell = document.createElement('td');
            let statusSummary = 'N/A';
            
            if (submission.result && submission.result.includes('<?xml')) {
                try {
                    const parser = new DOMParser();
                    const xmlDoc = parser.parseFromString(submission.result, "text/xml");
                    const dataAssessment = xmlDoc.querySelector('DataAssessmentStatus')?.textContent || 'N/A';
                    const benchmarkStatus = xmlDoc.querySelector('BenchmarkStatus')?.textContent || 'N/A';
                    const benchmarkIcon = benchmarkStatus === 'GREEN' ? '🟢' : 
                                        benchmarkStatus === 'YELLOW' ? '🟡' : 
                                        benchmarkStatus === 'RED' ? '🔴' : '';
                    statusSummary = `${dataAssessment} ${benchmarkIcon}`;
                } catch (e) {
                    statusSummary = 'Parse Error';
                }
            }
            
            summaryCell.textContent = statusSummary;
            row.appendChild(summaryCell);
            
            tbody.appendChild(row);
            
            // Add click handler for row
            row.addEventListener('click', () => {
                showSubmissionDetails(submission);
            });
        });
    }
    
    table.appendChild(tbody);
    return table;
}

function createInfoSection(title, content) {
    const section = document.createElement('div');
    section.className = 'info-section';
    
    const header = document.createElement('h5');
    header.textContent = title;
    section.appendChild(header);
    
    if (content instanceof HTMLElement) {
        section.appendChild(content);
    } else if (typeof content === 'string') {
        const contentDiv = document.createElement('div');
        contentDiv.textContent = content;
        section.appendChild(contentDiv);
    }
    
    return section;
}

function createInfoGrid(items) {
    const grid = document.createElement('div');
    grid.className = 'info-grid';
    
    items.forEach(item => {
        const itemDiv = document.createElement('div');
        itemDiv.className = 'info-item';
        
        const strong = document.createElement('strong');
        strong.textContent = item.label;
        itemDiv.appendChild(strong);
        
        itemDiv.appendChild(document.createTextNode(' '));
        
        if (item.isCode) {
            const code = document.createElement('code');
            code.textContent = item.value;
            itemDiv.appendChild(code);
        } else if (item.className) {
            const span = document.createElement('span');
            span.className = item.className;
            span.textContent = item.value;
            itemDiv.appendChild(span);
        } else {
            itemDiv.appendChild(document.createTextNode(item.value));
        }
        
        grid.appendChild(itemDiv);
    });
    
    return grid;
}

function createPatientDetailsTable(caseData) {
    const patient = caseData.patient;
    const verifiedIcon = caseData.verified ? '✅' : '❌';
    const verifiedText = caseData.verified ? 'Verified' : 'Not Verified';
    
    const table = document.createElement('table');
    table.className = 'patient-details-table';
    
    // Create table body
    const tbody = document.createElement('tbody');
    
    // Helper function to create table row
    function createTableRow(label, value, isCode = false, className = '') {
        const row = document.createElement('tr');
        
        const labelCell = document.createElement('td');
        labelCell.className = 'label-cell';
        labelCell.textContent = label;
        
        const valueCell = document.createElement('td');
        valueCell.className = 'value-cell';
        
        if (isCode) {
            const codeElement = document.createElement('code');
            codeElement.textContent = value;
            valueCell.appendChild(codeElement);
        } else if (className) {
            const span = document.createElement('span');
            span.className = className;
            span.textContent = value;
            valueCell.appendChild(span);
        } else {
            valueCell.textContent = value;
        }
        
        row.appendChild(labelCell);
        row.appendChild(valueCell);
        tbody.appendChild(row);
    }
    
    // Patient Demographics
    createTableRow('Patient Name', patient.name || 'N/A');
    createTableRow('Patient ID', patient.id || 'N/A', true);
    createTableRow('Age', patient.age || 'N/A');
    createTableRow('Sex', patient.sex || 'N/A');
    
    // Case Details
    createTableRow('Case ID', caseData.case_id, true);
    createTableRow('Procedure Date', caseData.procedure_date || 'N/A');
    createTableRow('Procedure Time', caseData.procedure_time || 'N/A');
    createTableRow('Verification Status', `${verifiedIcon} ${verifiedText}`, false, caseData.verified ? 'verified' : 'not-verified');
    
    // Clinical Details
    const chaScore = patient.cha2ds2_vasc || {};
    const chaDetails = chaScore.calculation ? Object.entries(chaScore.calculation)
        .filter(([key, value]) => typeof value === 'object' && value.isChecked)
        .map(([key, value]) => key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase()))
        .join(', ') || 'None' : 'N/A';
    
    const anticoagulants = patient.anticoagulation && Array.isArray(patient.anticoagulation) && patient.anticoagulation.length > 0 ? 
        patient.anticoagulation.map(med => med.name || 'Unknown').join(', ') : 'None';
    
    createTableRow('CHA2DS2-VASc Score', chaScore.score || 'N/A');
    createTableRow('Risk Factors', chaDetails);
    createTableRow('Anticoagulation', anticoagulants);
    createTableRow('A-Fib Ablation', patient.afib_ablation ? 'Yes' : 'No');
    
    // NCDR Submission Status
    const lastSubmission = caseData.ncdr_registry_status?.last_submission;
    if (lastSubmission) {
        const statusIcon = lastSubmission.status === 'Completed' ? '✅' : 
                          lastSubmission.status === 'Pending' ? '⏳' : 
                          lastSubmission.status === 'Failed' ? '❌' : '❓';
        createTableRow('Last Submission Status', `${statusIcon} ${lastSubmission.status}`);
        createTableRow('Last Submission ID', lastSubmission.submission_id, true);
        createTableRow('Last Submission Date', new Date(lastSubmission.timestamp).toLocaleString());
        
        // Parse result if available
        if (lastSubmission.result && lastSubmission.result.includes('<?xml')) {
            try {
                const parser = new DOMParser();
                const xmlDoc = parser.parseFromString(lastSubmission.result, "text/xml");
                const dataAssessment = xmlDoc.querySelector('DataAssessmentStatus')?.textContent || 'N/A';
                const benchmarkStatus = xmlDoc.querySelector('BenchmarkStatus')?.textContent || 'N/A';
                const benchmarkIcon = benchmarkStatus === 'GREEN' ? '🟢' : 
                                    benchmarkStatus === 'YELLOW' ? '🟡' : 
                                    benchmarkStatus === 'RED' ? '🔴' : '';
                
                createTableRow('Data Assessment', dataAssessment);
                createTableRow('Benchmark Status', `${benchmarkIcon} ${benchmarkStatus}`);
            } catch (e) {
                createTableRow('Result Status', 'Parse Error');
            }
        }
    } else {
        createTableRow('Last Submission Status', 'No previous submissions');
    }
    
    // Total submissions count
    const submissionHistory = caseData.ncdr_registry_status?.submission_history || [];
    createTableRow('Total Submissions', submissionHistory.length.toString());
    
    table.appendChild(tbody);
    return table;
}

function showPatientInfo(caseData) {
    if (!caseData || !caseData.patient) return;

    // Clear existing content
    patientInfo.innerHTML = '';

    // Create main container
    const container = document.createElement('div');
    container.className = 'patient-details-container';

    // Create main header
    const mainHeader = document.createElement('h4');
    const icon = document.createElement('i');
    icon.className = 'fas fa-user';
    mainHeader.appendChild(icon);
    mainHeader.appendChild(document.createTextNode(' Patient Information'));
    container.appendChild(mainHeader);

    // Create patient details table
    const detailsTable = createPatientDetailsTable(caseData);
    container.appendChild(detailsTable);

    // Submission History section (keep the existing table for history)
    const submissionHistory = caseData.ncdr_registry_status?.submission_history || [];
    if (submissionHistory.length > 0) {
        const historyHeader = document.createElement('h5');
        historyHeader.className = 'history-header';
        historyHeader.innerHTML = '<i class="fas fa-history"></i> Submission History';
        container.appendChild(historyHeader);
        
        const historyTable = createSubmissionHistoryTable(submissionHistory);
        container.appendChild(historyTable);
    }

    patientInfo.appendChild(container);
    patientInfo.style.display = 'block';
    patientInfo.classList.add('fade-in');
}

function hidePatientInfo() {
    patientInfo.style.display = 'none';
    patientInfo.classList.remove('fade-in');
}

// Parse XML response to user-friendly format
function parseNCDRStatusXML(xmlString) {
    try {
        const parser = new DOMParser();
        const xmlDoc = parser.parseFromString(xmlString, "text/xml");
        
        // Extract key information
        const registryId = xmlDoc.querySelector('RegistryId')?.textContent || 'N/A';
        const partName = xmlDoc.querySelector('PartName')?.textContent || 'N/A';
        const timeframe = xmlDoc.querySelector('Timeframe')?.textContent || 'N/A';
        const submissionId = xmlDoc.querySelector('Xmsnld')?.textContent || 'N/A';
        const uploadTime = xmlDoc.querySelector('DQRUploadedTime')?.textContent || 'N/A';
        
        // Submission Status
        const patientCount = xmlDoc.querySelector('PatientCount')?.textContent || 'N/A';
        const dataAssessment = xmlDoc.querySelector('DataAssessmentStatus')?.textContent || 'N/A';
        const completenessAssessment = xmlDoc.querySelector('CompletenessAssessmentStatus')?.textContent || 'N/A';
        const benchmarkStatus = xmlDoc.querySelector('BenchmarkStatus')?.textContent || 'N/A';
        const populationStatus = xmlDoc.querySelector('PopulationStatus')?.textContent || 'N/A';
        
        // Completeness Assessments
        const composites = Array.from(xmlDoc.querySelectorAll('Composite CompositeName'))
            .map(node => node.textContent)
            .join(', ');

        return {
            registryId,
            partName,
            timeframe,
            submissionId,
            uploadTime,
            patientCount,
            dataAssessment,
            completenessAssessment,
            benchmarkStatus,
            populationStatus,
            composites
        };
    } catch (error) {
        console.error('Error parsing XML:', error);
        return null;
    }
}

// Format submission status for display
function formatSubmissionStatus(parsedData) {
    if (!parsedData) return 'Unable to parse submission status';

    const statusIcon = parsedData.dataAssessment === 'Pass' ? '✅' : '❌';
    const benchmarkIcon = parsedData.benchmarkStatus === 'GREEN' ? '🟢' : 
                          parsedData.benchmarkStatus === 'YELLOW' ? '🟡' : '🔴';

    return `${statusIcon} NCDR Submission Status Report

📊 Registry Information:
• Registry ID: ${parsedData.registryId}
• Facility: ${parsedData.partName}
• Timeframe: ${parsedData.timeframe}
• Submission ID: ${parsedData.submissionId}

⏰ Upload Details:
• Upload Time: ${new Date(parsedData.uploadTime).toLocaleString()}
• Patient Count: ${parsedData.patientCount}

🔍 Assessment Results:
• Data Assessment: ${parsedData.dataAssessment === 'Pass' ? '✅ PASSED' : '❌ FAILED'}
• Completeness Assessment: ${parsedData.completenessAssessment === 'Pass' ? '✅ PASSED' : '❌ FAILED'}
• Benchmark Status: ${benchmarkIcon} ${parsedData.benchmarkStatus}
• Population Status: ${parsedData.populationStatus}

📋 Completeness Composites:
• ${parsedData.composites || 'None specified'}`;
}

// Format submission success message
function formatSubmissionSuccess(result, caseId) {
    const status = result.status || result['status '] || 'Unknown';
    const submissionId = result.submission_id;
    
    let statusIcon = '✅';
    let statusText = 'SUCCESSFUL';
    
    if (status.includes('SUCCESSFUL')) {
        statusIcon = '✅';
        statusText = 'SUCCESSFUL';
    } else if (status.includes('PENDING')) {
        statusIcon = '⏳';
        statusText = 'PENDING';
    } else if (status.includes('FAILED')) {
        statusIcon = '❌';
        statusText = 'FAILED';
    }

    return `${statusIcon} NCDR Submission ${statusText}

📤 Submission Details:
• Status: ${status}
• Submission ID: ${submissionId || 'Not provided'}
• Case ID: ${caseId}
• Timestamp: ${new Date().toLocaleString()}

${status.includes('SUCCESSFUL') ? '🎉 Your case has been successfully submitted to NCDR!' : ''}
${status.includes('PENDING') ? '⏳ Your submission is being processed. Please check status later.' : ''}`;
}

async function loadCases() {
    try {
        const startDate = document.getElementById("startDate").value;
        const endDate = document.getElementById("endDate").value;

        if (!startDate || !endDate) {
            showToast("Please select both start and end dates.", 'error');
            return;
        }

        showLoading('Loading patients...');
        console.log("Loading cases for date range:", startDate, "to", endDate);

        const access_token = localStorage.getItem("access_token");
        if (!access_token) {
            hideLoading();
            showToast("No access token found. Please login again.", 'error');
            return;
        }

        const apiUrl = `${config.apiUrl}/clinical/schedules?type=completed&completed_start_date=${startDate}&completed_end_date=${endDate}`;
        console.log("Fetching patients from:", apiUrl);

        const res = await fetch(apiUrl, {
            method: "GET",
            headers: {
                "Content-Type": "application/json",
                Authorization: `Bearer ${access_token}`,
            },
        });

        if (!res.ok) {
            throw new Error(`HTTP error! status: ${res.status}`);
        }

        const json = await res.json();
        const cases = json.result;

        if (!Array.isArray(cases)) {
            throw new Error("Expected result to be an array");
        }

        // Store cases for later reference
        loadedCases = cases;
        console.log("Loaded cases:", loadedCases);

        // Clear existing options
        caseSelect.innerHTML = '';
        
        // Add default option
        const defaultOption = document.createElement('option');
        defaultOption.value = '';
        defaultOption.textContent = '-- Select Patient --';
        caseSelect.appendChild(defaultOption);

        cases.forEach((item) => {
            const option = document.createElement('option');
            option.value = item.case_id;
            
            // Add verification status to display
            const verifiedIcon = item.verified ? '✅' : '❌';
            option.textContent = `${verifiedIcon} ${item.patient?.name || 'Unknown Patient'}`;
            
            // Disable option if not verified
            if (!item.verified) {
                option.disabled = true;
                option.style.color = '#999';
                option.style.fontStyle = 'italic';
            }
            
            caseSelect.appendChild(option);
        });

        hideLoading();
        updateButtonStates();
        
        const verifiedCount = cases.filter(c => c.verified).length;
        const totalCount = cases.length;
        showToast(`Loaded ${totalCount} patients (${verifiedCount} verified, ${totalCount - verifiedCount} unverified)`, 'success');

    } catch (err) {
        hideLoading();
        console.error("Fetch error:", err);
        showToast(`Failed to load patients: ${err.message}`, 'error');
    }
}

async function submitToNCDR() {
    const caseId = caseSelect.value;
    console.log("Submitting case_id:", caseId);

    if (!caseId) {
        showToast("Please select a patient first.", 'error');
        return;
    }

    // Check if selected case is verified
    const selectedCase = loadedCases.find(c => c.case_id === caseId);
    if (selectedCase && !selectedCase.verified) {
        showToast("Cannot submit unverified patient case.", 'error');
        return;
    }

    try {
        showLoading('Submitting to NCDR...');

        const apiUrl = `${config.apiUrl}/ncdr/cases/${caseId}/submit`;
        const access_token = localStorage.getItem("access_token");

        if (!access_token) {
            hideLoading();
            showToast("No access token found. Please login again.", 'error');
            return;
        }

        const res = await fetch(apiUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                Authorization: `Bearer ${access_token}`,
            },
        });

        const data = await res.json();
        hideLoading();

        if (res.ok) {
            // Handle successful response
            const result = data.result || {};
            const message = data.message || 'Submission completed';
            const formattedResult = formatSubmissionSuccess(result, caseId);

            showResult(formattedResult, 'success');
            showToast('Successfully submitted to NCDR!', 'success');

        } else {
            // Handle error response
            const error = data.error_description || data.message || 'Unknown error occurred';
            const displayText = `❌ NCDR Submission Failed

🚫 Error Details:
• Error: ${error}
• Case ID: ${caseId}
• Timestamp: ${new Date().toLocaleString()}

💡 Please check the case data and try again.`;

            showResult(displayText, 'error');
            showToast('Submission failed. Please try again.', 'error');
        }

    } catch (err) {
        hideLoading();
        console.error('Submit error:', err);

        const displayText = `❌ Network Connection Error

🌐 Connection Details:
• Error: ${err.message}
• Case ID: ${caseId}
• Timestamp: ${new Date().toLocaleString()}

💡 Please check your internet connection and try again.`;
        
        showResult(displayText, 'error');
        showToast('Network error occurred. Please check your connection.', 'error');
    }
}

async function checkSubmissionStatus() {
    const caseId = caseSelect.value;
    console.log("Checking status for case_id:", caseId);

    if (!caseId) {
        showToast("Please select a patient first.", 'error');
        return;
    }

    try {
        showLoading('Checking submission status...');

        const apiUrl = `${config.apiUrl}/ncdr/cases/${caseId}/status`;
        const access_token = localStorage.getItem("access_token");

        if (!access_token) {
            hideLoading();
            showToast("No access token found. Please login again.", 'error');
            return;
        }

        const res = await fetch(apiUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                Authorization: `Bearer ${access_token}`,
            },
        });

        const data = await res.json();
        hideLoading();

        if (res.ok) {
            const result = data.result?.result || data.result || 'No status information available';

            let displayText;
            if (typeof result === 'string' && result.includes('<?xml')) {
                // Parse XML response
                const parsedData = parseNCDRStatusXML(result);
                displayText = formatSubmissionStatus(parsedData);
            } else {
                // Handle other response formats
                const formattedResult = typeof result === 'string' ? 
                    result.replace(/\\r\\n/g, "\n").replace(/\\n/g, "\n").replace(/\t/g, "    ") :
                    JSON.stringify(result, null, 2);

                displayText = `📊 NCDR Submission Status

📋 Case Information:
• Case ID: ${caseId}
• Check Time: ${new Date().toLocaleString()}

📄 Raw Response:
${formattedResult}`;
            }

            showResult(displayText, 'info');
            showToast('Status retrieved successfully', 'success');

        } else {
            const error = data.error_description || data.message || 'Failed to retrieve status';
            const displayText = `❌ Status Check Failed

🚫 Error Details:
• Error: ${error}
• Case ID: ${caseId}
• Timestamp: ${new Date().toLocaleString()}

💡 Please try again or contact support if the issue persists.`;

            showResult(displayText, 'error');
            showToast('Failed to check status. Please try again.', 'error');
        }

    } catch (err) {
        hideLoading();
        console.error('Status check error:', err);

        const displayText = `❌ Network Connection Error

🌐 Connection Details:
• Error: ${err.message}
• Case ID: ${caseId}
• Timestamp: ${new Date().toLocaleString()}

💡 Please check your internet connection and try again.`;
        
        showResult(displayText, 'error');
        showToast('Network error occurred. Please check your connection.', 'error');
    }
}

function showResult(content, type = 'info') {
    resultDiv.className = `result-container result-${type}`;
    resultDiv.textContent = content;
    resultSection.style.display = 'block';
    resultSection.classList.add('fade-in');

    // Scroll to results
    resultSection.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
}

function clearResults() {
    resultSection.classList.add('fade-out');
    setTimeout(() => {
        resultSection.style.display = 'none';
        resultSection.classList.remove('fade-out', 'fade-in');
        resultDiv.textContent = '';
        resultDiv.className = 'result-container';
    }, 300);
}

// Show submission details in a modal or detailed view
function showSubmissionDetails(submission) {
    // Create a simple alert with submission details for now
    // In a real implementation, this could be a modal dialog
    let details = `Submission Details:\n\n`;
    details += `Status: ${submission.status}\n`;
    details += `Submission ID: ${submission.submission_id}\n`;
    details += `Date: ${new Date(submission.timestamp).toLocaleString()}\n\n`;
    
    if (submission.result && submission.result.includes('<?xml')) {
        try {
            const parser = new DOMParser();
            const xmlDoc = parser.parseFromString(submission.result, "text/xml");
            const dataAssessment = xmlDoc.querySelector('DataAssessmentStatus')?.textContent || 'N/A';
            const benchmarkStatus = xmlDoc.querySelector('BenchmarkStatus')?.textContent || 'N/A';
            
            details += `Data Assessment: ${dataAssessment}\n`;
            details += `Benchmark Status: ${benchmarkStatus}\n`;
            
            // Add more XML details if available
            const registryId = xmlDoc.querySelector('RegistryId')?.textContent;
            const patientCount = xmlDoc.querySelector('PatientCount')?.textContent;
            
            if (registryId) details += `Registry ID: ${registryId}\n`;
            if (patientCount) details += `Patient Count: ${patientCount}\n`;
            
        } catch (e) {
            details += `Result: Parse Error\n`;
        }
    } else if (submission.result) {
        details += `Result: ${submission.result.substring(0, 200)}${submission.result.length > 200 ? '...' : ''}\n`;
    }
    
    alert(details);
    console.log('Show submission details:', submission);
}

// Event Listeners
loadPatientsBtn.addEventListener('click', loadCases);
submitBtn.addEventListener('click', submitToNCDR);
checkStatusBtn.addEventListener('click', checkSubmissionStatus);
clearResultBtn.addEventListener('click', clearResults);

// Date change listeners
document.getElementById('startDate').addEventListener('change', updateButtonStates);
document.getElementById('endDate').addEventListener('change', updateButtonStates);

// Case selection listener
caseSelect.addEventListener('change', () => {
    const selectedCaseId = caseSelect.value;

    if (selectedCaseId) {
        // Find and display patient info
        const selectedCase = loadedCases.find(c => c.case_id === selectedCaseId);
        if (selectedCase) {
            showPatientInfo(selectedCase);
        }
    } else {
        hidePatientInfo();
    }

    updateButtonStates();
    clearResults(); // Clear previous results when changing selection
});

// Initialize on page load
document.addEventListener('DOMContentLoaded', () => {
    // Set default dates (last 7 days)
    const endDate = new Date();
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - 7);

    document.getElementById('endDate').value = endDate.toISOString().split('T')[0];
    document.getElementById('startDate').value = startDate.toISOString().split('T')[0];

    updateButtonStates();

    // Auto-load cases with default date range
    setTimeout(() => {
        loadCases();
    }, 500);
});

// Handle keyboard shortcuts
document.addEventListener('keydown', (e) => {
    if (e.ctrlKey || e.metaKey) {
        switch(e.key) {
            case 'Enter':
                if (!submitBtn.disabled) {
                    e.preventDefault();
                    submitToNCDR();
                }
                break;
            case 'r':
                e.preventDefault();
                loadCases();
                break;
        }
    }
});