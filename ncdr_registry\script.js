// DOM Elements
const caseSelect = document.getElementById('caseSelect');
const resultDiv = document.getElementById('result');
const resultSection = document.getElementById('result-section');
const clearResultBtn = document.getElementById('clearResultBtn');
const loadingOverlay = document.getElementById('loading-overlay');
const loadingText = document.getElementById('loading-text');
const toast = document.getElementById('toast');
const patientInfo = document.getElementById('patient-info');

// Buttons
const loadPatientsBtn = document.getElementById('loadPatientsBtn');
const submitBtn = document.getElementById('submitBtn');
const checkStatusBtn = document.getElementById('checkStatusBtn');
const logoutBtn = document.getElementById('logoutBtn');

// Store loaded cases for reference
let loadedCases = [];

// Utility Functions
function showLoading(message = 'Loading...') {
    loadingText.textContent = message;
    loadingOverlay.style.display = 'flex';
    setTimeout(() => {
        loadingOverlay.style.opacity = '1';
    }, 10);
}

function hideLoading() {
    loadingOverlay.style.opacity = '0';
    setTimeout(() => {
        loadingOverlay.style.display = 'none';
    }, 300);
}

function showToast(message, type = 'info') {
    toast.textContent = message;
    toast.className = `toast ${type}`;
    toast.classList.add('show');

    setTimeout(() => {
        toast.classList.remove('show');
    }, 4000);
}

function updateButtonStates() {
    const hasDateRange = document.getElementById('startDate').value && document.getElementById('endDate').value;
    const hasSelectedCase = caseSelect.value;

    caseSelect.disabled = !hasDateRange;
    submitBtn.disabled = !hasSelectedCase;
    checkStatusBtn.disabled = !hasSelectedCase;

    if (!hasDateRange) {
        caseSelect.innerHTML = '<option value="">-- Select Date Range First --</option>';
        hidePatientInfo();
    }
}

function showPatientInfo(caseData) {
    if (!caseData || !caseData.patient) return;

    const patient = caseData.patient;
    patientInfo.innerHTML = `
        <h4><i class="fas fa-user"></i> Patient Information</h4>
        <p><strong>Name:</strong> ${patient.name || 'N/A'}</p>
        <p><strong>Case ID:</strong> ${caseData.case_id}</p>
        <p><strong>Date:</strong> ${caseData.scheduled_date || 'N/A'}</p>
        <p><strong>Status:</strong> ${caseData.status || 'N/A'}</p>
    `;
    patientInfo.style.display = 'block';
    patientInfo.classList.add('fade-in');
}

function hidePatientInfo() {
    patientInfo.style.display = 'none';
    patientInfo.classList.remove('fade-in');
}

async function loadCases() {
    try {
        const startDate = document.getElementById("startDate").value;
        const endDate = document.getElementById("endDate").value;

        if (!startDate || !endDate) {
            showToast("Please select both start and end dates.", 'error');
            return;
        }

        showLoading('Loading patients...');
        console.log("Loading cases for date range:", startDate, "to", endDate);

        const access_token = localStorage.getItem("access_token");
        if (!access_token) {
            hideLoading();
            showToast("No access token found. Please login again.", 'error');
            return;
        }

        const apiUrl = `${config.apiUrl}/clinical/schedules?type=completed&completed_start_date=${startDate}&completed_end_date=${endDate}`;
        console.log("Fetching patients from:", apiUrl);

        const res = await fetch(apiUrl, {
            method: "GET",
            headers: {
                "Content-Type": "application/json",
                Authorization: `Bearer ${access_token}`,
            },
        });

        if (!res.ok) {
            throw new Error(`HTTP error! status: ${res.status}`);
        }

        const json = await res.json();
        const cases = json.result;

        if (!Array.isArray(cases)) {
            throw new Error("Expected result to be an array");
        }

        // Store cases for later reference
        loadedCases = cases;

        caseSelect.innerHTML = '<option value="">-- Select Patient --</option>';
        cases.forEach((item) => {
            const option = document.createElement("option");
            option.value = item.case_id;
            option.text = `${item.patient?.name || "Unnamed Patient"} (${item.case_id})`;
            caseSelect.appendChild(option);
        });

        hideLoading();
        updateButtonStates();
        showToast(`Loaded ${cases.length} patients successfully`, 'success');

    } catch (err) {
        hideLoading();
        console.error("Fetch error:", err);
        showToast(`Failed to load patients: ${err.message}`, 'error');
    }
}
  

async function submitToNCDR() {
    const caseId = caseSelect.value;
    console.log("Submitting case_id:", caseId);

    if (!caseId) {
        showToast("Please select a patient first.", 'error');
        return;
    }

    try {
        showLoading('Submitting to NCDR...');

        const apiUrl = `${config.apiUrl}/ncdr/cases/${caseId}/submit`;
        const access_token = localStorage.getItem("access_token");

        if (!access_token) {
            hideLoading();
            showToast("No access token found. Please login again.", 'error');
            return;
        }

        const res = await fetch(apiUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                Authorization: `Bearer ${access_token}`,
            },
        });

        const data = await res.json();
        hideLoading();

        if (res.ok) {
            // Handle successful response
            const result = data.result || {};
            const status = result.status || result['status '] || 'Unknown'; // Handle both 'status' and 'status ' (with space)
            const submissionId = result.submission_id;
            const message = data.message || 'Submission completed';

            let displayText = `✅ ${message}\n\n`;
            displayText += `Status: ${status}\n`;
            if (submissionId) {
                displayText += `Submission ID: ${submissionId}\n`;
            }
            displayText += `Case ID: ${caseId}\n`;
            displayText += `Timestamp: ${new Date().toLocaleString()}`;

            showResult(displayText, 'success');
            showToast('Successfully submitted to NCDR!', 'success');

        } else {
            // Handle error response
            const error = data.error_description || data.message || 'Unknown error occurred';
            const displayText = `❌ Submission Failed\n\nError: ${error}\nCase ID: ${caseId}\nTimestamp: ${new Date().toLocaleString()}`;

            showResult(displayText, 'error');
            showToast('Submission failed. Please try again.', 'error');
        }

    } catch (err) {
        hideLoading();
        console.error('Submit error:', err);

        const displayText = `❌ Network Error\n\nError: ${err.message}\nCase ID: ${caseId}\nTimestamp: ${new Date().toLocaleString()}`;
        showResult(displayText, 'error');
        showToast('Network error occurred. Please check your connection.', 'error');
    }
}

async function checkSubmissionStatus() {
    const caseId = caseSelect.value;
    console.log("Checking status for case_id:", caseId);

    if (!caseId) {
        showToast("Please select a patient first.", 'error');
        return;
    }

    try {
        showLoading('Checking submission status...');

        const apiUrl = `${config.apiUrl}/ncdr/cases/${caseId}/status`;
        const access_token = localStorage.getItem("access_token");

        if (!access_token) {
            hideLoading();
            showToast("No access token found. Please login again.", 'error');
            return;
        }

        const res = await fetch(apiUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                Authorization: `Bearer ${access_token}`,
            },
        });

        const data = await res.json();
        hideLoading();

        if (res.ok) {
            const result = data.result?.result || data.result || 'No status information available';

            // Format XML/text response for better readability
            let formattedResult;
            if (typeof result === 'string') {
                formattedResult = result
                    .replace(/\\r\\n/g, "\n")
                    .replace(/\\n/g, "\n")
                    .replace(/\t/g, "    ")
                    .replace(/&/g, "&amp;")
                    .replace(/</g, "&lt;")
                    .replace(/>/g, "&gt;");
            } else {
                formattedResult = JSON.stringify(result, null, 2);
            }

            const displayText = `📊 Submission Status\n\nCase ID: ${caseId}\nTimestamp: ${new Date().toLocaleString()}\n\n${formattedResult}`;
            showResult(displayText, 'info');
            showToast('Status retrieved successfully', 'success');

        } else {
            const error = data.error_description || data.message || 'Failed to retrieve status';
            const displayText = `❌ Status Check Failed\n\nError: ${error}\nCase ID: ${caseId}\nTimestamp: ${new Date().toLocaleString()}`;

            showResult(displayText, 'error');
            showToast('Failed to check status. Please try again.', 'error');
        }

    } catch (err) {
        hideLoading();
        console.error('Status check error:', err);

        const displayText = `❌ Network Error\n\nError: ${err.message}\nCase ID: ${caseId}\nTimestamp: ${new Date().toLocaleString()}`;
        showResult(displayText, 'error');
        showToast('Network error occurred. Please check your connection.', 'error');
    }
}

function showResult(content, type = 'info') {
    resultDiv.className = `result-container result-${type}`;
    resultDiv.textContent = content;
    resultSection.style.display = 'block';
    resultSection.classList.add('fade-in');

    // Scroll to results
    resultSection.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
}

function clearResults() {
    resultSection.classList.add('fade-out');
    setTimeout(() => {
        resultSection.style.display = 'none';
        resultSection.classList.remove('fade-out', 'fade-in');
        resultDiv.textContent = '';
        resultDiv.className = 'result-container';
    }, 300);
}

// Logout function
async function handleLogout() {
    // Show confirmation dialog
    const confirmed = confirm('Are you sure you want to logout?');
    if (!confirmed) {
        return;
    }

    try {
        showLoading('Logging out...');
        showToast('Logging out...', 'info');

        // Use the existing Apilogout function from api.js
        await Apilogout();

    } catch (error) {
        hideLoading();
        console.error('Logout error:', error);

        // Fallback: clear localStorage and redirect
        localStorage.clear();
        showToast('Logged out successfully', 'success');
        setTimeout(() => {
            window.location.href = "/login.html";
        }, 1000);
    }
}

// Event Listeners
loadPatientsBtn.addEventListener('click', loadCases);
submitBtn.addEventListener('click', submitToNCDR);
checkStatusBtn.addEventListener('click', checkSubmissionStatus);
clearResultBtn.addEventListener('click', clearResults);
logoutBtn.addEventListener('click', handleLogout);

// Date change listeners
document.getElementById('startDate').addEventListener('change', updateButtonStates);
document.getElementById('endDate').addEventListener('change', updateButtonStates);

// Case selection listener
caseSelect.addEventListener('change', () => {
    const selectedCaseId = caseSelect.value;

    if (selectedCaseId) {
        // Find and display patient info
        const selectedCase = loadedCases.find(c => c.case_id === selectedCaseId);
        if (selectedCase) {
            showPatientInfo(selectedCase);
        }
    } else {
        hidePatientInfo();
    }

    updateButtonStates();
    clearResults(); // Clear previous results when changing selection
});

// Initialize on page load
document.addEventListener('DOMContentLoaded', () => {
    // Set default dates (last 7 days)
    const endDate = new Date();
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - 7);

    document.getElementById('endDate').value = endDate.toISOString().split('T')[0];
    document.getElementById('startDate').value = startDate.toISOString().split('T')[0];

    updateButtonStates();

    // Auto-load cases with default date range
    setTimeout(() => {
        loadCases();
    }, 500);
});

// Handle keyboard shortcuts
document.addEventListener('keydown', (e) => {
    if (e.ctrlKey || e.metaKey) {
        switch(e.key) {
            case 'Enter':
                if (!submitBtn.disabled) {
                    e.preventDefault();
                    submitToNCDR();
                }
                break;
            case 'r':
                e.preventDefault();
                loadCases();
                break;
        }
    }
});
