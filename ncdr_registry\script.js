// DOM Elements
const resultDiv = document.getElementById('result');
const resultSection = document.getElementById('result-section');
const clearResultBtn = document.getElementById('clearResultBtn');
const loadingOverlay = document.getElementById('loading-overlay');
const loadingText = document.getElementById('loading-text');
const toast = document.getElementById('toast');

// Modal Elements
const submissionModal = document.getElementById('submissionModal');
const modalTitle = document.getElementById('modalTitle');
const modalContent = document.getElementById('modalContent');
const modalCloseBtn = document.getElementById('modalCloseBtn');
const modalOkBtn = document.getElementById('modalOkBtn');

// New Table Elements
const patientsTableContainer = document.getElementById('patientsTableContainer');
const noDataMessage = document.getElementById('noDataMessage');
const bulkActions = document.getElementById('bulkActions');
const selectedCount = document.getElementById('selectedCount');
const statusFilter = document.getElementById('statusFilter');
const ncdrFilter = document.getElementById('ncdrFilter');

// Buttons
const loadPatientsBtn = document.getElementById('loadPatientsBtn');
const refreshBtn = document.getElementById('refreshBtn');
const bulkSubmitBtn = document.getElementById('bulkSubmitBtn');
const bulkStatusBtn = document.getElementById('bulkStatusBtn');
const exportBtn = document.getElementById('exportBtn');

// Store loaded cases and table state
let loadedCases = [];
let filteredCases = [];
let selectedCases = new Set();
let sortColumn = null;
let sortDirection = 'asc';

// Utility Functions
function showLoading(message = 'Loading...') {
    loadingText.textContent = message;
    loadingOverlay.style.display = 'flex';
    setTimeout(() => {
        loadingOverlay.style.opacity = '1';
    }, 10);
}

function hideLoading() {
    loadingOverlay.style.opacity = '0';
    setTimeout(() => {
        loadingOverlay.style.display = 'none';
    }, 300);
}

function showToast(message, type = 'info') {
    toast.textContent = message;
    toast.className = `toast ${type}`;
    toast.classList.add('show');

    setTimeout(() => {
        toast.classList.remove('show');
    }, 4000);
}

// Modal utility functions
function showModal(title, content) {
    modalTitle.innerHTML = `<i class="fas fa-info-circle"></i> ${title}`;
    modalContent.innerHTML = content;
    submissionModal.style.display = 'flex';
    setTimeout(() => {
        submissionModal.classList.add('show');
    }, 10);
}

function hideModal() {
    submissionModal.classList.remove('show');
    setTimeout(() => {
        submissionModal.style.display = 'none';
    }, 300);
}

function updateButtonStates() {
    const hasDateRange = document.getElementById('startDate').value && document.getElementById('endDate').value;
    const hasSelectedCases = selectedCases.size > 0;

    // Update bulk action buttons
    bulkSubmitBtn.disabled = !hasSelectedCases;
    bulkStatusBtn.disabled = !hasSelectedCases;
    exportBtn.disabled = loadedCases.length === 0;

    // Update selected count
    selectedCount.textContent = `${selectedCases.size} selected`;

    // Show/hide bulk actions
    bulkActions.style.display = hasSelectedCases ? 'block' : 'none';

    if (!hasDateRange) {
        showNoDataMessage();
    }
}

// Show no data message
function showNoDataMessage() {
    patientsTableContainer.innerHTML = `
        <div class="no-data-message">
            <i class="fas fa-calendar-alt"></i>
            <h4>Select Date Range</h4>
            <p>Please select a date range above to load patients</p>
        </div>
    `;
}

// Show loading state for table
function showTableLoading() {
    patientsTableContainer.innerHTML = `
        <div class="table-loading">
            <div class="loader"></div>
            <p>Loading patients...</p>
        </div>
    `;
}

// Show empty state when no patients found
function showEmptyState() {
    patientsTableContainer.innerHTML = `
        <div class="empty-state">
            <i class="fas fa-user-slash"></i>
            <h4>No Patients Found</h4>
            <p>No patients found for the selected date range</p>
        </div>
    `;
}

// Create patients table
function createPatientsTable(cases) {
    if (!cases || cases.length === 0) {
        showEmptyState();
        return;
    }

    const table = document.createElement('table');
    table.className = 'patients-table';

    // Create table header
    const thead = document.createElement('thead');
    thead.innerHTML = `
        <tr>
            <th class="checkbox-cell">
                <input type="checkbox" id="selectAll" class="row-checkbox">
            </th>
            <th class="sortable" data-column="patient_name">
                Patient Name <span class="sort-icon"></span>
            </th>
            <th class="sortable" data-column="case_id">
                Case ID <span class="sort-icon"></span>
            </th>
            <th class="sortable" data-column="verification_status">
                Verification <span class="sort-icon"></span>
            </th>
            <th class="sortable" data-column="ncdr_status">
                NCDR Status <span class="sort-icon"></span>
            </th>
            <th class="sortable" data-column="last_submission">
                Last Submission <span class="sort-icon"></span>
            </th>
            <th>Actions</th>
        </tr>
    `;

    // Create table body
    const tbody = document.createElement('tbody');
    cases.forEach(caseData => {
        const row = createPatientRow(caseData);
        tbody.appendChild(row);
    });

    table.appendChild(thead);
    table.appendChild(tbody);

    patientsTableContainer.innerHTML = '';
    patientsTableContainer.appendChild(table);

    // Add event listeners
    addTableEventListeners();
}

// Create individual patient row
function createPatientRow(caseData) {
    const row = document.createElement('tr');
    row.dataset.caseId = caseData.case_id;

    const patient = caseData.patient || {};
    const patientName = patient.name || 'Unknown Patient';
    const patientId = patient.patient_id || 'N/A';

    // Determine verification status
    const isVerified = caseData.verification_status === 'verified' ||
                      (caseData.verified && caseData.verified.value === 'True');

    // Determine NCDR status (this would come from submission history)
    const ncdrStatus = caseData.ncdr_status || 'not-sent';
    const lastSubmission = caseData.last_submission || null;

    row.innerHTML = `
        <td class="checkbox-cell">
            <input type="checkbox" class="row-checkbox" data-case-id="${caseData.case_id}">
        </td>
        <td>
            <div class="patient-name">${patientName}</div>
            <div class="patient-id">ID: ${patientId}</div>
        </td>
        <td>
            <span class="case-id" onclick="showCaseDetails('${caseData.case_id}')">${caseData.case_id}</span>
        </td>
        <td>
            <span class="status-badge ${isVerified ? 'status-verified' : 'status-not-verified'}">
                ${isVerified ? '✅ Verified' : '❌ Not Verified'}
            </span>
        </td>
        <td>
            <span class="status-badge ${getNCDRStatusClass(ncdrStatus)}">
                ${getNCDRStatusText(ncdrStatus)}
            </span>
        </td>
        <td>
            <div class="last-submission">
                ${lastSubmission ? `
                    <div class="submission-time">${new Date(lastSubmission.timestamp).toLocaleDateString()}</div>
                    <div class="submission-status">${lastSubmission.status}</div>
                ` : '<span style="color: #999;">Never</span>'}
            </div>
        </td>
        <td>
            <div class="action-buttons">
                <button class="action-btn submit" onclick="submitSingleCase('${caseData.case_id}')"
                        title="Submit to NCDR">
                    <i class="fas fa-upload"></i>
                    Submit
                </button>
                <button class="action-btn status" onclick="checkSingleStatus('${caseData.case_id}')"
                        title="Check Status">
                    <i class="fas fa-search"></i>
                    Status
                </button>
                <button class="action-btn details" onclick="showCaseDetails('${caseData.case_id}')"
                        title="View Details">
                    <i class="fas fa-info"></i>
                    Details
                </button>
            </div>
        </td>
    `;

    return row;
}

// Helper functions for NCDR status
function getNCDRStatusClass(status) {
    switch (status) {
        case 'submitted': return 'status-submitted';
        case 'pending': return 'status-pending';
        case 'failed': return 'status-failed';
        default: return 'status-not-sent';
    }
}

function getNCDRStatusText(status) {
    switch (status) {
        case 'submitted': return '✅ Submitted';
        case 'pending': return '⏳ Pending';
        case 'failed': return '❌ Failed';
        default: return '➖ Not Sent';
    }
}

// Add event listeners to table
function addTableEventListeners() {
    // Select all checkbox
    const selectAllCheckbox = document.getElementById('selectAll');
    if (selectAllCheckbox) {
        selectAllCheckbox.addEventListener('change', (e) => {
            const checkboxes = document.querySelectorAll('.row-checkbox[data-case-id]');
            checkboxes.forEach(checkbox => {
                checkbox.checked = e.target.checked;
                if (e.target.checked) {
                    selectedCases.add(checkbox.dataset.caseId);
                } else {
                    selectedCases.delete(checkbox.dataset.caseId);
                }
            });
            updateButtonStates();
        });
    }

    // Individual row checkboxes
    const rowCheckboxes = document.querySelectorAll('.row-checkbox[data-case-id]');
    rowCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', (e) => {
            if (e.target.checked) {
                selectedCases.add(e.target.dataset.caseId);
            } else {
                selectedCases.delete(e.target.dataset.caseId);
            }
            updateButtonStates();

            // Update select all checkbox
            const allCheckboxes = document.querySelectorAll('.row-checkbox[data-case-id]');
            const checkedCheckboxes = document.querySelectorAll('.row-checkbox[data-case-id]:checked');
            if (selectAllCheckbox) {
                selectAllCheckbox.checked = allCheckboxes.length === checkedCheckboxes.length;
                selectAllCheckbox.indeterminate = checkedCheckboxes.length > 0 && checkedCheckboxes.length < allCheckboxes.length;
            }
        });
    });

    // Sortable columns
    const sortableHeaders = document.querySelectorAll('.sortable');
    sortableHeaders.forEach(header => {
        header.addEventListener('click', () => {
            const column = header.dataset.column;
            if (sortColumn === column) {
                sortDirection = sortDirection === 'asc' ? 'desc' : 'asc';
            } else {
                sortColumn = column;
                sortDirection = 'asc';
            }

            // Update sort indicators
            sortableHeaders.forEach(h => h.classList.remove('sort-asc', 'sort-desc'));
            header.classList.add(`sort-${sortDirection}`);

            // Sort and re-render table
            sortAndFilterCases();
        });
    });
}

// Sort and filter cases
function sortAndFilterCases() {
    let filtered = [...loadedCases];

    // Apply filters
    const statusFilterValue = statusFilter.value;
    const ncdrFilterValue = ncdrFilter.value;

    if (statusFilterValue) {
        filtered = filtered.filter(caseData => {
            const isVerified = caseData.verification_status === 'verified' ||
                              (caseData.verified && caseData.verified.value === 'True');
            return statusFilterValue === 'verified' ? isVerified : !isVerified;
        });
    }

    if (ncdrFilterValue) {
        filtered = filtered.filter(caseData => {
            const ncdrStatus = caseData.ncdr_status || 'not-sent';
            return ncdrStatus === ncdrFilterValue;
        });
    }

    // Apply sorting
    if (sortColumn) {
        filtered.sort((a, b) => {
            let aValue, bValue;

            switch (sortColumn) {
                case 'patient_name':
                    aValue = (a.patient?.name || '').toLowerCase();
                    bValue = (b.patient?.name || '').toLowerCase();
                    break;
                case 'case_id':
                    aValue = a.case_id;
                    bValue = b.case_id;
                    break;
                case 'verification_status':
                    aValue = a.verification_status === 'verified' || (a.verified && a.verified.value === 'True') ? 1 : 0;
                    bValue = b.verification_status === 'verified' || (b.verified && b.verified.value === 'True') ? 1 : 0;
                    break;
                case 'ncdr_status':
                    aValue = a.ncdr_status || 'not-sent';
                    bValue = b.ncdr_status || 'not-sent';
                    break;
                case 'last_submission':
                    aValue = a.last_submission ? new Date(a.last_submission.timestamp) : new Date(0);
                    bValue = b.last_submission ? new Date(b.last_submission.timestamp) : new Date(0);
                    break;
                default:
                    return 0;
            }

            if (aValue < bValue) return sortDirection === 'asc' ? -1 : 1;
            if (aValue > bValue) return sortDirection === 'asc' ? 1 : -1;
            return 0;
        });
    }

    filteredCases = filtered;
    createPatientsTable(filteredCases);
}

function createSubmissionHistoryTable(history) {
    const table = document.createElement('table');
    table.className = 'submission-history-table';
    
    // Create header
    const thead = document.createElement('thead');
    const headerRow = document.createElement('tr');
    
    const headers = ['Status', 'Submission ID', 'Date & Time', 'Result Summary'];
    headers.forEach(headerText => {
        const th = document.createElement('th');
        th.textContent = headerText;
        headerRow.appendChild(th);
    });
    
    thead.appendChild(headerRow);
    table.appendChild(thead);
    
    // Create body
    const tbody = document.createElement('tbody');
    
    if (!history || !Array.isArray(history) || history.length === 0) {
        const row = document.createElement('tr');
        const cell = document.createElement('td');
        cell.colSpan = 4;
        cell.style.textAlign = 'center';
        cell.style.color = '#666';
        cell.style.fontStyle = 'italic';
        cell.textContent = 'No submission history available';
        row.appendChild(cell);
        tbody.appendChild(row);
    } else {
        history.forEach((submission, index) => {
            const row = document.createElement('tr');
            row.className = 'submission-row';
            row.setAttribute('data-index', index);
            
            // Status cell
            const statusCell = document.createElement('td');
            const statusIcon = submission.status === 'Completed' ? '✅' : 
                              submission.status === 'Pending' ? '⏳' : 
                              submission.status === 'Failed' ? '❌' : '❓';
            statusCell.textContent = `${statusIcon} ${submission.status}`;
            row.appendChild(statusCell);
            
            // Submission ID cell
            const idCell = document.createElement('td');
            const codeElement = document.createElement('code');
            codeElement.textContent = submission.submission_id;
            idCell.appendChild(codeElement);
            row.appendChild(idCell);
            
            // Date cell
            const dateCell = document.createElement('td');
            const formattedDate = new Date(submission.timestamp).toLocaleString();
            dateCell.textContent = formattedDate;
            row.appendChild(dateCell);
            
            // Status summary cell
            const summaryCell = document.createElement('td');
            let statusSummary = 'N/A';
            
            if (submission.result && submission.result.includes('<?xml')) {
                try {
                    const parser = new DOMParser();
                    const xmlDoc = parser.parseFromString(submission.result, "text/xml");
                    const dataAssessment = xmlDoc.querySelector('DataAssessmentStatus')?.textContent || 'N/A';
                    const benchmarkStatus = xmlDoc.querySelector('BenchmarkStatus')?.textContent || 'N/A';
                    const benchmarkIcon = benchmarkStatus === 'GREEN' ? '🟢' : 
                                        benchmarkStatus === 'YELLOW' ? '🟡' : 
                                        benchmarkStatus === 'RED' ? '🔴' : '';
                    statusSummary = `${dataAssessment} ${benchmarkIcon}`;
                } catch (e) {
                    statusSummary = 'Parse Error';
                }
            }
            
            summaryCell.textContent = statusSummary;
            row.appendChild(summaryCell);
            
            tbody.appendChild(row);
            
            // Add click handler for row
            row.addEventListener('click', () => {
                showSubmissionDetails(submission);
            });
        });
    }
    
    table.appendChild(tbody);
    return table;
}

function createInfoSection(title, content) {
    const section = document.createElement('div');
    section.className = 'info-section';
    
    const header = document.createElement('h5');
    header.textContent = title;
    section.appendChild(header);
    
    if (content instanceof HTMLElement) {
        section.appendChild(content);
    } else if (typeof content === 'string') {
        const contentDiv = document.createElement('div');
        contentDiv.textContent = content;
        section.appendChild(contentDiv);
    }
    
    return section;
}

function createInfoGrid(items) {
    const grid = document.createElement('div');
    grid.className = 'info-grid';
    
    items.forEach(item => {
        const itemDiv = document.createElement('div');
        itemDiv.className = 'info-item';
        
        const strong = document.createElement('strong');
        strong.textContent = item.label;
        itemDiv.appendChild(strong);
        
        itemDiv.appendChild(document.createTextNode(' '));
        
        if (item.isCode) {
            const code = document.createElement('code');
            code.textContent = item.value;
            itemDiv.appendChild(code);
        } else if (item.className) {
            const span = document.createElement('span');
            span.className = item.className;
            span.textContent = item.value;
            itemDiv.appendChild(span);
        } else {
            itemDiv.appendChild(document.createTextNode(item.value));
        }
        
        grid.appendChild(itemDiv);
    });
    
    return grid;
}

function createPatientDetailsTable(caseData) {
    const patient = caseData.patient;
    const verifiedIcon = caseData.verified ? '✅' : '❌';
    const verifiedText = caseData.verified ? 'Verified' : 'Not Verified';
    
    const table = document.createElement('table');
    table.className = 'patient-details-table';
    
    // Create table body
    const tbody = document.createElement('tbody');
    
    // Helper function to create table row
    function createTableRow(label, value, isCode = false, className = '') {
        const row = document.createElement('tr');
        
        const labelCell = document.createElement('td');
        labelCell.className = 'label-cell';
        labelCell.textContent = label;
        
        const valueCell = document.createElement('td');
        valueCell.className = 'value-cell';
        
        if (isCode) {
            const codeElement = document.createElement('code');
            codeElement.textContent = value;
            valueCell.appendChild(codeElement);
        } else if (className) {
            const span = document.createElement('span');
            span.className = className;
            span.textContent = value;
            valueCell.appendChild(span);
        } else {
            valueCell.textContent = value;
        }
        
        row.appendChild(labelCell);
        row.appendChild(valueCell);
        tbody.appendChild(row);
    }
    
    // Patient Demographics
    createTableRow('Patient Name', patient.name || 'N/A');
    createTableRow('Patient ID', patient.id || 'N/A', true);
    createTableRow('Age', patient.age || 'N/A');
    createTableRow('Sex', patient.sex || 'N/A');
    
    // Case Details
    createTableRow('Case ID', caseData.case_id, true);
    createTableRow('Procedure Date', caseData.procedure_date || 'N/A');
    createTableRow('Procedure Time', caseData.procedure_time || 'N/A');
    createTableRow('Verification Status', `${verifiedIcon} ${verifiedText}`, false, caseData.verified ? 'verified' : 'not-verified');
    
    // Clinical Details
    const chaScore = patient.cha2ds2_vasc || {};
    const chaDetails = chaScore.calculation ? Object.entries(chaScore.calculation)
        .filter(([key, value]) => typeof value === 'object' && value.isChecked)
        .map(([key, value]) => key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase()))
        .join(', ') || 'None' : 'N/A';
    
    const anticoagulants = patient.anticoagulation && Array.isArray(patient.anticoagulation) && patient.anticoagulation.length > 0 ? 
        patient.anticoagulation.map(med => med.name || 'Unknown').join(', ') : 'None';
    
    createTableRow('CHA2DS2-VASc Score', chaScore.score || 'N/A');
    createTableRow('Risk Factors', chaDetails);
    createTableRow('Anticoagulation', anticoagulants);
    createTableRow('A-Fib Ablation', patient.afib_ablation ? 'Yes' : 'No');
    
    // NCDR Submission Status
    const lastSubmission = caseData.ncdr_registry_status?.last_submission;
    if (lastSubmission) {
        const statusIcon = lastSubmission.status === 'Completed' ? '✅' : 
                          lastSubmission.status === 'Pending' ? '⏳' : 
                          lastSubmission.status === 'Failed' ? '❌' : '❓';
        createTableRow('Last Submission Status', `${statusIcon} ${lastSubmission.status}`);
        createTableRow('Last Submission ID', lastSubmission.submission_id, true);
        createTableRow('Last Submission Date', new Date(lastSubmission.timestamp).toLocaleString());
        
        // Parse result if available
        if (lastSubmission.result && lastSubmission.result.includes('<?xml')) {
            try {
                const parser = new DOMParser();
                const xmlDoc = parser.parseFromString(lastSubmission.result, "text/xml");
                const dataAssessment = xmlDoc.querySelector('DataAssessmentStatus')?.textContent || 'N/A';
                const benchmarkStatus = xmlDoc.querySelector('BenchmarkStatus')?.textContent || 'N/A';
                const benchmarkIcon = benchmarkStatus === 'GREEN' ? '🟢' : 
                                    benchmarkStatus === 'YELLOW' ? '🟡' : 
                                    benchmarkStatus === 'RED' ? '🔴' : '';
                
                createTableRow('Data Assessment', dataAssessment);
                createTableRow('Benchmark Status', `${benchmarkIcon} ${benchmarkStatus}`);
            } catch (e) {
                createTableRow('Result Status', 'Parse Error');
            }
        }
    } else {
        createTableRow('Last Submission Status', 'No previous submissions');
    }
    
    // Total submissions count
    const submissionHistory = caseData.ncdr_registry_status?.submission_history || [];
    createTableRow('Total Submissions', submissionHistory.length.toString());
    
    table.appendChild(tbody);
    return table;
}

function showPatientInfo(caseData) {
    if (!caseData || !caseData.patient) return;

    // Clear existing content
    patientInfo.innerHTML = '';

    // Create main container
    const container = document.createElement('div');
    container.className = 'patient-details-container';

    // Create main header
    const mainHeader = document.createElement('h4');
    const icon = document.createElement('i');
    icon.className = 'fas fa-user';
    mainHeader.appendChild(icon);
    mainHeader.appendChild(document.createTextNode(' Patient Information'));
    container.appendChild(mainHeader);

    // Create patient details table
    const detailsTable = createPatientDetailsTable(caseData);
    container.appendChild(detailsTable);

    // Submission History section (keep the existing table for history)
    const submissionHistory = caseData.ncdr_registry_status?.submission_history || [];
    if (submissionHistory.length > 0) {
        const historyHeader = document.createElement('h5');
        historyHeader.className = 'history-header';
        historyHeader.innerHTML = '<i class="fas fa-history"></i> Submission History';
        container.appendChild(historyHeader);
        
        const historyTable = createSubmissionHistoryTable(submissionHistory);
        container.appendChild(historyTable);
    }

    patientInfo.appendChild(container);
    patientInfo.style.display = 'block';
    patientInfo.classList.add('fade-in');
}

function hidePatientInfo() {
    patientInfo.style.display = 'none';
    patientInfo.classList.remove('fade-in');
}

// Parse XML response to user-friendly format
function parseNCDRStatusXML(xmlString) {
    try {
        const parser = new DOMParser();
        const xmlDoc = parser.parseFromString(xmlString, "text/xml");
        
        // Extract key information
        const registryId = xmlDoc.querySelector('RegistryId')?.textContent || 'N/A';
        const partName = xmlDoc.querySelector('PartName')?.textContent || 'N/A';
        const timeframe = xmlDoc.querySelector('Timeframe')?.textContent || 'N/A';
        const submissionId = xmlDoc.querySelector('Xmsnld')?.textContent || 'N/A';
        const uploadTime = xmlDoc.querySelector('DQRUploadedTime')?.textContent || 'N/A';
        
        // Submission Status
        const patientCount = xmlDoc.querySelector('PatientCount')?.textContent || 'N/A';
        const dataAssessment = xmlDoc.querySelector('DataAssessmentStatus')?.textContent || 'N/A';
        const completenessAssessment = xmlDoc.querySelector('CompletenessAssessmentStatus')?.textContent || 'N/A';
        const benchmarkStatus = xmlDoc.querySelector('BenchmarkStatus')?.textContent || 'N/A';
        const populationStatus = xmlDoc.querySelector('PopulationStatus')?.textContent || 'N/A';
        
        // Completeness Assessments
        const composites = Array.from(xmlDoc.querySelectorAll('Composite CompositeName'))
            .map(node => node.textContent)
            .join(', ');

        return {
            registryId,
            partName,
            timeframe,
            submissionId,
            uploadTime,
            patientCount,
            dataAssessment,
            completenessAssessment,
            benchmarkStatus,
            populationStatus,
            composites
        };
    } catch (error) {
        console.error('Error parsing XML:', error);
        return null;
    }
}

// Format submission status for display
function formatSubmissionStatus(parsedData) {
    if (!parsedData) return 'Unable to parse submission status';

    const statusIcon = parsedData.dataAssessment === 'Pass' ? '✅' : '❌';
    const benchmarkIcon = parsedData.benchmarkStatus === 'GREEN' ? '🟢' : 
                          parsedData.benchmarkStatus === 'YELLOW' ? '🟡' : '🔴';

    return `${statusIcon} NCDR Submission Status Report

📊 Registry Information:
• Registry ID: ${parsedData.registryId}
• Facility: ${parsedData.partName}
• Timeframe: ${parsedData.timeframe}
• Submission ID: ${parsedData.submissionId}

⏰ Upload Details:
• Upload Time: ${new Date(parsedData.uploadTime).toLocaleString()}
• Patient Count: ${parsedData.patientCount}

🔍 Assessment Results:
• Data Assessment: ${parsedData.dataAssessment === 'Pass' ? '✅ PASSED' : '❌ FAILED'}
• Completeness Assessment: ${parsedData.completenessAssessment === 'Pass' ? '✅ PASSED' : '❌ FAILED'}
• Benchmark Status: ${benchmarkIcon} ${parsedData.benchmarkStatus}
• Population Status: ${parsedData.populationStatus}

📋 Completeness Composites:
• ${parsedData.composites || 'None specified'}`;
}

// Format submission success message
function formatSubmissionSuccess(result, caseId) {
    const status = result.status || result['status '] || 'Unknown';
    const submissionId = result.submission_id;
    
    let statusIcon = '✅';
    let statusText = 'SUCCESSFUL';
    
    if (status.includes('SUCCESSFUL')) {
        statusIcon = '✅';
        statusText = 'SUCCESSFUL';
    } else if (status.includes('PENDING')) {
        statusIcon = '⏳';
        statusText = 'PENDING';
    } else if (status.includes('FAILED')) {
        statusIcon = '❌';
        statusText = 'FAILED';
    }

    return `${statusIcon} NCDR Submission ${statusText}

📤 Submission Details:
• Status: ${status}
• Submission ID: ${submissionId || 'Not provided'}
• Case ID: ${caseId}
• Timestamp: ${new Date().toLocaleString()}

${status.includes('SUCCESSFUL') ? '🎉 Your case has been successfully submitted to NCDR!' : ''}
${status.includes('PENDING') ? '⏳ Your submission is being processed. Please check status later.' : ''}`;
}

async function loadCases() {
    try {
        const startDate = document.getElementById("startDate").value;
        const endDate = document.getElementById("endDate").value;

        if (!startDate || !endDate) {
            showToast("Please select both start and end dates.", 'error');
            return;
        }

        showLoading('Loading patients...');
        console.log("Loading cases for date range:", startDate, "to", endDate);

        const access_token = localStorage.getItem("access_token");
        if (!access_token) {
            hideLoading();
            showToast("No access token found. Please login again.", 'error');
            return;
        }

        const apiUrl = `${config.apiUrl}/clinical/schedules?type=completed&completed_start_date=${startDate}&completed_end_date=${endDate}`;
        console.log("Fetching patients from:", apiUrl);

        const res = await fetch(apiUrl, {
            method: "GET",
            headers: {
                "Content-Type": "application/json",
                Authorization: `Bearer ${access_token}`,
            },
        });

        if (!res.ok) {
            throw new Error(`HTTP error! status: ${res.status}`);
        }

        const json = await res.json();
        const cases = json.result;

        if (!Array.isArray(cases)) {
            throw new Error("Expected result to be an array");
        }

        // Store cases for later reference
        loadedCases = cases;
        selectedCases.clear(); // Clear previous selections
        console.log("Loaded cases:", loadedCases);

        // Create the patients table
        showTableLoading();
        setTimeout(() => {
            createPatientsTable(cases);
        }, 100);

        hideLoading();
        updateButtonStates();

        const verifiedCount = cases.filter(c => c.verified).length;
        const totalCount = cases.length;
        showToast(`Loaded ${totalCount} patients (${verifiedCount} verified, ${totalCount - verifiedCount} unverified)`, 'success');

    } catch (err) {
        hideLoading();
        console.error("Fetch error:", err);
        showToast(`Failed to load patients: ${err.message}`, 'error');
        showEmptyState();
    }
}

// Individual case actions
async function submitSingleCase(caseId) {
    try {
        showLoading('Submitting to NCDR...');

        const apiUrl = `${config.apiUrl}/ncdr/cases/${caseId}/submit`;
        const access_token = localStorage.getItem("access_token");

        if (!access_token) {
            hideLoading();
            showToast("No access token found. Please login again.", 'error');
            return;
        }

        const res = await fetch(apiUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                Authorization: `Bearer ${access_token}`,
            },
        });

        const data = await res.json();
        hideLoading();

        if (res.ok) {
            const result = data.result || {};
            const status = result.status || result['status '] || 'Unknown';
            const submissionId = result.submission_id;

            showToast(`Case ${caseId} submitted successfully!`, 'success');

            // Update the case status in the table
            updateCaseStatus(caseId, 'submitted', {
                timestamp: new Date().toISOString(),
                status: status,
                submission_id: submissionId
            });

        } else {
            const error = data.error_description || data.message || 'Unknown error occurred';
            showToast(`Failed to submit case ${caseId}: ${error}`, 'error');
        }

    } catch (err) {
        hideLoading();
        console.error('Submit error:', err);
        showToast(`Network error submitting case ${caseId}`, 'error');
    }
}

async function checkSingleStatus(caseId) {
    try {
        showLoading('Checking status...');

        const apiUrl = `${config.apiUrl}/ncdr/cases/${caseId}/status`;
        const access_token = localStorage.getItem("access_token");

        if (!access_token) {
            hideLoading();
            showToast("No access token found. Please login again.", 'error');
            return;
        }

        const res = await fetch(apiUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                Authorization: `Bearer ${access_token}`,
            },
        });

        const data = await res.json();
        hideLoading();

        if (res.ok) {
            const result = data.result?.result || data.result || 'No status information available';
            showSubmissionDetails({
                case_id: caseId,
                status: 'Status Check',
                submission_id: 'N/A',
                timestamp: new Date().toISOString(),
                result: result
            });
        } else {
            const error = data.error_description || data.message || 'Failed to retrieve status';
            showToast(`Failed to check status for case ${caseId}: ${error}`, 'error');
        }

    } catch (err) {
        hideLoading();
        console.error('Status check error:', err);
        showToast(`Network error checking status for case ${caseId}`, 'error');
    }
}

function showCaseDetails(caseId) {
    const caseData = loadedCases.find(c => c.case_id === caseId);
    if (!caseData) {
        showToast('Case not found', 'error');
        return;
    }

    const patient = caseData.patient || {};
    const modalContent = `
        <div class="case-details">
            <div class="detail-section">
                <h4><i class="fas fa-user"></i> Patient Information</h4>
                <div class="detail-grid">
                    <div class="detail-item">
                        <span class="detail-label">Name:</span>
                        <span class="detail-value">${patient.name || 'N/A'}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">Patient ID:</span>
                        <span class="detail-value">${patient.patient_id || 'N/A'}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">Case ID:</span>
                        <span class="detail-value">${caseData.case_id}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">Date:</span>
                        <span class="detail-value">${caseData.scheduled_date || 'N/A'}</span>
                    </div>
                </div>
            </div>

            <div class="detail-section">
                <h4><i class="fas fa-check-circle"></i> Verification Status</h4>
                <div class="verification-status">
                    ${caseData.verified ?
                        '<span class="status-badge status-verified">✅ Verified</span>' :
                        '<span class="status-badge status-not-verified">❌ Not Verified</span>'
                    }
                </div>
            </div>
        </div>
    `;

    showModal(`Case Details - ${caseId}`, modalContent);
}

// Update case status in the table
function updateCaseStatus(caseId, ncdrStatus, submissionData) {
    // Update the loaded cases data
    const caseIndex = loadedCases.findIndex(c => c.case_id === caseId);
    if (caseIndex !== -1) {
        loadedCases[caseIndex].ncdr_status = ncdrStatus;
        loadedCases[caseIndex].last_submission = submissionData;
    }

    // Update the table row
    const row = document.querySelector(`tr[data-case-id="${caseId}"]`);
    if (row) {
        const statusCell = row.querySelector('td:nth-child(5)');
        const lastSubmissionCell = row.querySelector('td:nth-child(6)');

        if (statusCell) {
            statusCell.innerHTML = `
                <span class="status-badge ${getNCDRStatusClass(ncdrStatus)}">
                    ${getNCDRStatusText(ncdrStatus)}
                </span>
            `;
        }

        if (lastSubmissionCell && submissionData) {
            lastSubmissionCell.innerHTML = `
                <div class="last-submission">
                    <div class="submission-time">${new Date(submissionData.timestamp).toLocaleDateString()}</div>
                    <div class="submission-status">${submissionData.status}</div>
                </div>
            `;
        }
    }
}

// Bulk operations
async function bulkSubmitCases() {
    if (selectedCases.size === 0) {
        showToast('No cases selected', 'error');
        return;
    }

    const confirmed = confirm(`Are you sure you want to submit ${selectedCases.size} cases to NCDR?`);
    if (!confirmed) return;

    showLoading(`Submitting ${selectedCases.size} cases...`);

    let successCount = 0;
    let errorCount = 0;
    const results = [];

    for (const caseId of selectedCases) {
        try {
            const apiUrl = `${config.apiUrl}/ncdr/cases/${caseId}/submit`;
            const access_token = localStorage.getItem("access_token");

            const res = await fetch(apiUrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    Authorization: `Bearer ${access_token}`,
                },
            });

            const data = await res.json();

            if (res.ok) {
                successCount++;
                const result = data.result || {};
                const status = result.status || result['status '] || 'Unknown';
                const submissionId = result.submission_id;

                updateCaseStatus(caseId, 'submitted', {
                    timestamp: new Date().toISOString(),
                    status: status,
                    submission_id: submissionId
                });

                results.push({ caseId, success: true, message: status });
            } else {
                errorCount++;
                const error = data.error_description || data.message || 'Unknown error';
                results.push({ caseId, success: false, message: error });
            }
        } catch (err) {
            errorCount++;
            results.push({ caseId, success: false, message: err.message });
        }
    }

    hideLoading();

    // Show summary
    const summaryMessage = `Bulk submission completed:\n✅ ${successCount} successful\n❌ ${errorCount} failed`;
    showToast(summaryMessage, successCount > 0 ? 'success' : 'error');

    // Clear selections
    selectedCases.clear();
    updateButtonStates();

    // Update table
    const checkboxes = document.querySelectorAll('.row-checkbox[data-case-id]');
    checkboxes.forEach(cb => cb.checked = false);
    const selectAllCheckbox = document.getElementById('selectAll');
    if (selectAllCheckbox) selectAllCheckbox.checked = false;
}

async function bulkCheckStatus() {
    if (selectedCases.size === 0) {
        showToast('No cases selected', 'error');
        return;
    }

    showLoading(`Checking status for ${selectedCases.size} cases...`);

    const results = [];

    for (const caseId of selectedCases) {
        try {
            const apiUrl = `${config.apiUrl}/ncdr/cases/${caseId}/status`;
            const access_token = localStorage.getItem("access_token");

            const res = await fetch(apiUrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    Authorization: `Bearer ${access_token}`,
                },
            });

            const data = await res.json();

            if (res.ok) {
                const result = data.result?.result || data.result || 'No status available';
                results.push({ caseId, success: true, result });
            } else {
                const error = data.error_description || data.message || 'Failed to retrieve status';
                results.push({ caseId, success: false, error });
            }
        } catch (err) {
            results.push({ caseId, success: false, error: err.message });
        }
    }

    hideLoading();

    // Show results in modal
    const modalContent = `
        <div class="bulk-status-results">
            <h4>Status Check Results (${selectedCases.size} cases)</h4>
            <div class="results-list">
                ${results.map(r => `
                    <div class="result-item ${r.success ? 'success' : 'error'}">
                        <strong>Case ${r.caseId}:</strong>
                        ${r.success ?
                            `<pre class="status-result">${typeof r.result === 'string' ? r.result.substring(0, 200) + '...' : JSON.stringify(r.result, null, 2)}</pre>` :
                            `<span class="error-text">${r.error}</span>`
                        }
                    </div>
                `).join('')}
            </div>
        </div>
    `;

    showModal('Bulk Status Check Results', modalContent);
}

function exportData() {
    if (loadedCases.length === 0) {
        showToast('No data to export', 'error');
        return;
    }

    const csvData = [
        ['Patient Name', 'Case ID', 'Patient ID', 'Verification Status', 'NCDR Status', 'Last Submission Date', 'Last Submission Status']
    ];

    loadedCases.forEach(caseData => {
        const patient = caseData.patient || {};
        const isVerified = caseData.verified ? 'Verified' : 'Not Verified';
        const ncdrStatus = getNCDRStatusText(caseData.ncdr_status || 'not-sent').replace(/[✅❌⏳➖]/g, '').trim();
        const lastSubmissionDate = caseData.last_submission ?
            new Date(caseData.last_submission.timestamp).toLocaleDateString() : 'Never';
        const lastSubmissionStatus = caseData.last_submission ? caseData.last_submission.status : 'N/A';

        csvData.push([
            patient.name || 'Unknown',
            caseData.case_id,
            patient.patient_id || 'N/A',
            isVerified,
            ncdrStatus,
            lastSubmissionDate,
            lastSubmissionStatus
        ]);
    });

    const csvContent = csvData.map(row => row.map(cell => `"${cell}"`).join(',')).join('\n');
    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);

    const a = document.createElement('a');
    a.href = url;
    a.download = `ncdr-patients-${new Date().toISOString().split('T')[0]}.csv`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);

    showToast('Data exported successfully', 'success');
}

async function submitToNCDR() {
    const caseId = caseSelect.value;
    console.log("Submitting case_id:", caseId);

    if (!caseId) {
        showToast("Please select a patient first.", 'error');
        return;
    }

    // Check if selected case is verified
    const selectedCase = loadedCases.find(c => c.case_id === caseId);
    if (selectedCase && !selectedCase.verified) {
        showToast("Cannot submit unverified patient case.", 'error');
        return;
    }

    try {
        showLoading('Submitting to NCDR...');

        const apiUrl = `${config.apiUrl}/ncdr/cases/${caseId}/submit`;
        const access_token = localStorage.getItem("access_token");

        if (!access_token) {
            hideLoading();
            showToast("No access token found. Please login again.", 'error');
            return;
        }

        const res = await fetch(apiUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                Authorization: `Bearer ${access_token}`,
            },
        });

        const data = await res.json();
        hideLoading();

        if (res.ok) {
            // Handle successful response
            const result = data.result || {};
            const message = data.message || 'Submission completed';
            const formattedResult = formatSubmissionSuccess(result, caseId);

            showResult(formattedResult, 'success');
            showToast('Successfully submitted to NCDR!', 'success');

        } else {
            // Handle error response
            const error = data.error_description || data.message || 'Unknown error occurred';
            const displayText = `❌ NCDR Submission Failed

🚫 Error Details:
• Error: ${error}
• Case ID: ${caseId}
• Timestamp: ${new Date().toLocaleString()}

💡 Please check the case data and try again.`;

            showResult(displayText, 'error');
            showToast('Submission failed. Please try again.', 'error');
        }

    } catch (err) {
        hideLoading();
        console.error('Submit error:', err);

        const displayText = `❌ Network Connection Error

🌐 Connection Details:
• Error: ${err.message}
• Case ID: ${caseId}
• Timestamp: ${new Date().toLocaleString()}

💡 Please check your internet connection and try again.`;
        
        showResult(displayText, 'error');
        showToast('Network error occurred. Please check your connection.', 'error');
    }
}

async function checkSubmissionStatus() {
    const caseId = caseSelect.value;
    console.log("Checking status for case_id:", caseId);

    if (!caseId) {
        showToast("Please select a patient first.", 'error');
        return;
    }

    try {
        showLoading('Checking submission status...');

        const apiUrl = `${config.apiUrl}/ncdr/cases/${caseId}/status`;
        const access_token = localStorage.getItem("access_token");

        if (!access_token) {
            hideLoading();
            showToast("No access token found. Please login again.", 'error');
            return;
        }

        const res = await fetch(apiUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                Authorization: `Bearer ${access_token}`,
            },
        });

        const data = await res.json();
        hideLoading();

        if (res.ok) {
            const result = data.result?.result || data.result || 'No status information available';

            let displayText;
            if (typeof result === 'string' && result.includes('<?xml')) {
                // Parse XML response
                const parsedData = parseNCDRStatusXML(result);
                displayText = formatSubmissionStatus(parsedData);
            } else {
                // Handle other response formats
                const formattedResult = typeof result === 'string' ? 
                    result.replace(/\\r\\n/g, "\n").replace(/\\n/g, "\n").replace(/\t/g, "    ") :
                    JSON.stringify(result, null, 2);

                displayText = `📊 NCDR Submission Status

📋 Case Information:
• Case ID: ${caseId}
• Check Time: ${new Date().toLocaleString()}

📄 Raw Response:
${formattedResult}`;
            }

            showResult(displayText, 'info');
            showToast('Status retrieved successfully', 'success');

        } else {
            const error = data.error_description || data.message || 'Failed to retrieve status';
            const displayText = `❌ Status Check Failed

🚫 Error Details:
• Error: ${error}
• Case ID: ${caseId}
• Timestamp: ${new Date().toLocaleString()}

💡 Please try again or contact support if the issue persists.`;

            showResult(displayText, 'error');
            showToast('Failed to check status. Please try again.', 'error');
        }

    } catch (err) {
        hideLoading();
        console.error('Status check error:', err);

        const displayText = `❌ Network Connection Error

🌐 Connection Details:
• Error: ${err.message}
• Case ID: ${caseId}
• Timestamp: ${new Date().toLocaleString()}

💡 Please check your internet connection and try again.`;
        
        showResult(displayText, 'error');
        showToast('Network error occurred. Please check your connection.', 'error');
    }
}

function showResult(content, type = 'info') {
    resultDiv.className = `result-container result-${type}`;
    resultDiv.textContent = content;
    resultSection.style.display = 'block';
    resultSection.classList.add('fade-in');

    // Scroll to results
    resultSection.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
}

function clearResults() {
    resultSection.classList.add('fade-out');
    setTimeout(() => {
        resultSection.style.display = 'none';
        resultSection.classList.remove('fade-out', 'fade-in');
        resultDiv.textContent = '';
        resultDiv.className = 'result-container';
    }, 300);
}

// Show submission details in a custom modal
function showSubmissionDetails(submission) {
    const statusIcon = getStatusIcon(submission.status);
    const statusClass = getStatusClass(submission.status);

    let modalContent = `
        <div class="submission-detail-card">
            <div class="submission-header">
                <span class="status-badge ${statusClass}">
                    ${statusIcon} ${submission.status}
                </span>
                <span class="submission-date">
                    ${new Date(submission.timestamp).toLocaleString()}
                </span>
            </div>

            <div class="submission-info">
                <div class="info-row">
                    <span class="info-label">Submission ID:</span>
                    <span class="info-value">${submission.submission_id}</span>
                </div>
            </div>
    `;

    if (submission.result && submission.result.includes('<?xml')) {
        try {
            const parser = new DOMParser();
            const xmlDoc = parser.parseFromString(submission.result, "text/xml");
            const dataAssessment = xmlDoc.querySelector('DataAssessmentStatus')?.textContent || 'N/A';
            const benchmarkStatus = xmlDoc.querySelector('BenchmarkStatus')?.textContent || 'N/A';
            const registryId = xmlDoc.querySelector('RegistryId')?.textContent;
            const patientCount = xmlDoc.querySelector('PatientCount')?.textContent;

            modalContent += `
                <div class="assessment-results">
                    <h4><i class="fas fa-chart-line"></i> Assessment Results</h4>
                    <div class="info-row">
                        <span class="info-label">Data Assessment:</span>
                        <span class="info-value ${dataAssessment === 'Pass' ? 'success' : 'error'}">
                            ${dataAssessment === 'Pass' ? '✅' : '❌'} ${dataAssessment}
                        </span>
                    </div>
                    <div class="info-row">
                        <span class="info-label">Benchmark Status:</span>
                        <span class="info-value">${benchmarkStatus}</span>
                    </div>
                    ${registryId ? `
                        <div class="info-row">
                            <span class="info-label">Registry ID:</span>
                            <span class="info-value">${registryId}</span>
                        </div>
                    ` : ''}
                    ${patientCount ? `
                        <div class="info-row">
                            <span class="info-label">Patient Count:</span>
                            <span class="info-value">${patientCount}</span>
                        </div>
                    ` : ''}
                </div>
            `;

        } catch (e) {
            modalContent += `
                <div class="error-info">
                    <h4><i class="fas fa-exclamation-triangle"></i> Parse Error</h4>
                    <p>Unable to parse XML response</p>
                </div>
            `;
        }
    } else if (submission.result) {
        const truncatedResult = submission.result.length > 300 ?
            submission.result.substring(0, 300) + '...' : submission.result;

        modalContent += `
            <div class="raw-result">
                <h4><i class="fas fa-code"></i> Raw Result</h4>
                <pre class="result-text">${truncatedResult}</pre>
            </div>
        `;
    }

    modalContent += '</div>';

    showModal('Submission Details', modalContent);
}

// Helper functions for status styling
function getStatusIcon(status) {
    switch (status?.toLowerCase()) {
        case 'success':
        case 'completed':
            return '✅';
        case 'failed':
        case 'error':
            return '❌';
        case 'pending':
        case 'processing':
            return '⏳';
        default:
            return '📄';
    }
}

function getStatusClass(status) {
    switch (status?.toLowerCase()) {
        case 'success':
        case 'completed':
            return 'status-success';
        case 'failed':
        case 'error':
            return 'status-error';
        case 'pending':
        case 'processing':
            return 'status-pending';
        default:
            return 'status-default';
    }
}

// Event Listeners
loadPatientsBtn.addEventListener('click', loadCases);
refreshBtn.addEventListener('click', loadCases);
bulkSubmitBtn.addEventListener('click', bulkSubmitCases);
bulkStatusBtn.addEventListener('click', bulkCheckStatus);
exportBtn.addEventListener('click', exportData);
clearResultBtn.addEventListener('click', clearResults);

// Filter event listeners
statusFilter.addEventListener('change', sortAndFilterCases);
ncdrFilter.addEventListener('change', sortAndFilterCases);

// Modal event listeners
modalCloseBtn.addEventListener('click', hideModal);
modalOkBtn.addEventListener('click', hideModal);

// Close modal when clicking outside
submissionModal.addEventListener('click', (e) => {
    if (e.target === submissionModal) {
        hideModal();
    }
});

// Close modal with Escape key
document.addEventListener('keydown', (e) => {
    if (e.key === 'Escape' && submissionModal.classList.contains('show')) {
        hideModal();
    }
});

// Date change listeners
document.getElementById('startDate').addEventListener('change', updateButtonStates);
document.getElementById('endDate').addEventListener('change', updateButtonStates);

// Case selection listener
caseSelect.addEventListener('change', () => {
    const selectedCaseId = caseSelect.value;

    if (selectedCaseId) {
        // Find and display patient info
        const selectedCase = loadedCases.find(c => c.case_id === selectedCaseId);
        if (selectedCase) {
            showPatientInfo(selectedCase);
        }
    } else {
        hidePatientInfo();
    }

    updateButtonStates();
    clearResults(); // Clear previous results when changing selection
});

// Initialize on page load
document.addEventListener('DOMContentLoaded', () => {
    // Set default dates (last 7 days)
    const endDate = new Date();
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - 7);

    document.getElementById('endDate').value = endDate.toISOString().split('T')[0];
    document.getElementById('startDate').value = startDate.toISOString().split('T')[0];

    updateButtonStates();

    // Auto-load cases with default date range
    setTimeout(() => {
        loadCases();
    }, 500);
});

// Handle keyboard shortcuts
document.addEventListener('keydown', (e) => {
    if (e.ctrlKey || e.metaKey) {
        switch(e.key) {
            case 'Enter':
                if (!submitBtn.disabled) {
                    e.preventDefault();
                    submitToNCDR();
                }
                break;
            case 'r':
                e.preventDefault();
                loadCases();
                break;
        }
    }
});