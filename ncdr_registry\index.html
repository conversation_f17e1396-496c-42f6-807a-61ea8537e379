<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>NCDR Submission</title>
  <link rel="stylesheet" href="style.css">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css" />
</head>
<body>
  <!-- Loading Overlay -->
  <div id="loading-overlay" class="loading-overlay">
    <div class="loading-content">
      <div class="loader"></div>
      <p id="loading-text">Loading...</p>
    </div>
  </div>

  <div class="container">
    <div class="header">
      <i class="fas fa-file-medical-alt header-icon"></i>
      <h2>NCDR Case Submission</h2>
      <p class="subtitle">Submit patient cases to the National Cardiovascular Data Registry</p>
    </div>

    <!-- Date Range Section -->
    <div class="section">
      <h3><i class="fas fa-calendar-alt"></i> Select Date Range</h3>
      <div class="date-range">
        <div class="input-group">
          <label for="startDate">Start Date:</label>
          <input type="date" id="startDate" />
        </div>
        <div class="input-group">
          <label for="endDate">End Date:</label>
          <input type="date" id="endDate" />
        </div>
        <button id="loadPatientsBtn" class="btn-primary">
          <i class="fas fa-search"></i>
          <span>Load Patients</span>
        </button>
      </div>
    </div>

    <!-- Patients Table Section -->
    <div class="section">
      <div class="table-header">
        <h3><i class="fas fa-table"></i> Patients Overview</h3>
        <div class="table-controls">
          <div class="filter-controls">
            <select id="statusFilter" class="filter-select">
              <option value="">All Statuses</option>
              <option value="verified">✅ Verified</option>
              <option value="not-verified">❌ Not Verified</option>
            </select>
            <select id="ncdrFilter" class="filter-select">
              <option value="">All NCDR Status</option>
              <option value="submitted">✅ Submitted</option>
              <option value="pending">⏳ Pending</option>
              <option value="failed">❌ Failed</option>
              <option value="not-sent">➖ Not Sent</option>
            </select>
          </div>
          <div class="table-actions">
            <span id="selectedCount" class="selected-count">0 selected</span>
            <button id="refreshBtn" class="btn-secondary btn-small">
              <i class="fas fa-sync-alt"></i>
              Refresh
            </button>
          </div>
        </div>
      </div>

      <div id="patientsTableContainer" class="table-container">
        <div class="no-data-message" id="noDataMessage">
          <i class="fas fa-calendar-alt"></i>
          <h4>Select Date Range</h4>
          <p>Please select a date range above to load patients</p>
        </div>
      </div>

      <!-- Bulk Actions -->
      <div id="bulkActions" class="bulk-actions" style="display: none;">
        <div class="bulk-actions-content">
          <span class="bulk-label">Bulk Actions:</span>
          <button id="bulkSubmitBtn" class="btn-primary btn-small">
            <i class="fas fa-upload"></i>
            Submit Selected to NCDR
          </button>
          <button id="bulkStatusBtn" class="btn-secondary btn-small">
            <i class="fas fa-search"></i>
            Check Status for Selected
          </button>
          <button id="exportBtn" class="btn-secondary btn-small">
            <i class="fas fa-download"></i>
            Export Data
          </button>
        </div>
      </div>
    </div>

    <!-- Results Section -->
    <div id="result-section" class="section" style="display: none;">
      <h3><i class="fas fa-clipboard-check"></i> Results</h3>
      <div id="result" class="result-container"></div>
      <button id="clearResultBtn" class="btn-clear">
        <i class="fas fa-times"></i>
        Clear Results
      </button>
    </div>

    <!-- Success/Error Toast -->
    <div id="toast" class="toast"></div>
  </div>

  <!-- Custom Modal for Submission Details -->
  <div id="submissionModal" class="modal-overlay">
    <div class="modal-container">
      <div class="modal-header">
        <h3 id="modalTitle">
          <i class="fas fa-info-circle"></i>
          Submission Details
        </h3>
        <button id="modalCloseBtn" class="modal-close-btn">
          <i class="fas fa-times"></i>
        </button>
      </div>
      <div class="modal-body">
        <div id="modalContent"></div>
      </div>
      <div class="modal-footer">
        <button id="modalOkBtn" class="btn-primary">
          <i class="fas fa-check"></i>
          OK
        </button>
      </div>
    </div>
  </div>

  <script src="../config.js"></script>
  <script src="script.js"></script>
</body>
</html>
