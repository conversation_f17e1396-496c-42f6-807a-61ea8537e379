<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>NCDR Submission</title>
  <link rel="stylesheet" href="style.css">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css" />
</head>
<body>
  <!-- Loading Overlay -->
  <div id="loading-overlay" class="loading-overlay">
    <div class="loading-content">
      <div class="loader"></div>
      <p id="loading-text">Loading...</p>
    </div>
  </div>

  <div class="full-screen-container">
    <!-- Top Header Bar -->
    <div class="top-header">
      <div class="header-left">
        <i class="fas fa-file-medical-alt header-icon"></i>
        <div class="header-text">
          <h2>NCDR Case Submission</h2>
          <p class="subtitle">Submit patient cases to the National Cardiovascular Data Registry</p>
        </div>
      </div>

      <!-- Date Range Controls -->
      <div class="header-controls">
        <div class="date-range-compact">
          <div class="input-group-compact">
            <label for="startDate">From:</label>
            <input type="date" id="startDate" />
          </div>
          <div class="input-group-compact">
            <label for="endDate">To:</label>
            <input type="date" id="endDate" />
          </div>
          <button id="loadPatientsBtn" class="btn-primary-compact">
            <i class="fas fa-search"></i>
            Load Patients
          </button>
          <button id="refreshBtn" class="btn-secondary-compact">
            <i class="fas fa-sync-alt"></i>
          </button>
        </div>
      </div>
    </div>

    <!-- Main Content Area -->
    <div class="main-content">
      <!-- Filters and Controls Bar -->
      <div class="controls-bar">
        <div class="filter-section">
          <div class="filter-group">
            <label>Filter by Status:</label>
            <select id="statusFilter" class="filter-select">
              <option value="">All Verification Status</option>
              <option value="verified">✅ Verified Only</option>
              <option value="not-verified">❌ Not Verified Only</option>
            </select>
          </div>
          <div class="filter-group">
            <label>Filter by NCDR:</label>
            <select id="ncdrFilter" class="filter-select">
              <option value="">All NCDR Status</option>
              <option value="submitted">✅ Submitted</option>
              <option value="pending">⏳ Pending</option>
              <option value="failed">❌ Failed</option>
              <option value="not-sent">➖ Not Sent</option>
            </select>
          </div>
        </div>

        <div class="action-section">
          <span id="selectedCount" class="selected-count">0 selected</span>
          <div class="bulk-actions-inline" id="bulkActionsInline" style="display: none;">
            <button id="bulkSubmitBtn" class="btn-primary-small">
              <i class="fas fa-upload"></i>
              Submit Selected
            </button>
            <button id="bulkStatusBtn" class="btn-secondary-small">
              <i class="fas fa-search"></i>
              Check Status
            </button>
          </div>
          <button id="exportBtn" class="btn-secondary-small">
            <i class="fas fa-download"></i>
            Export
          </button>
        </div>
      </div>

      <!-- Full Screen Table Container -->
      <div id="patientsTableContainer" class="full-table-container">
        <div class="no-data-message" id="noDataMessage">
          <i class="fas fa-calendar-alt"></i>
          <h4>Select Date Range</h4>
          <p>Please select a date range above to load patients</p>
        </div>
      </div>
    </div>

    <!-- Success/Error Toast -->
    <div id="toast" class="toast"></div>
  </div>

  <!-- Custom Modal for Submission Details -->
  <div id="submissionModal" class="modal-overlay">
    <div class="modal-container">
      <div class="modal-header">
        <h3 id="modalTitle">
          <i class="fas fa-info-circle"></i>
          Submission Details
        </h3>
        <button id="modalCloseBtn" class="modal-close-btn">
          <i class="fas fa-times"></i>
        </button>
      </div>
      <div class="modal-body">
        <div id="modalContent"></div>
      </div>
      <div class="modal-footer">
        <button id="modalOkBtn" class="btn-primary">
          <i class="fas fa-check"></i>
          OK
        </button>
      </div>
    </div>
  </div>

  <script src="../config.js"></script>
  <script src="script.js"></script>
</body>
</html>
