import {
  formatHeading,
  updateTileStyle,
  validateStringInput,
  formatDisplayDate,
  formatDisplayDateTime,
} from "../utils.js";

// Function to clear values in conditional fields
function clearConditionalValues(field, conditionalKey) {
  if (!field[conditionalKey] || typeof field[conditionalKey] !== "object") {
    return;
  }

  // If it's a direct field with input_type (like a date field)
  if (field[conditionalKey].input_type) {
    field[conditionalKey].value = "";
    field[conditionalKey].modified_by = "";
  } else {
    // If it's an object with multiple sub-fields
    Object.keys(field[conditionalKey]).forEach((subKey) => {
      const subField = field[conditionalKey][subKey];
      if (subField && typeof subField === "object") {
        if (subField.input_type === "multi_select" && Array.isArray(subField.value)) {
          subField.value = [];
        } else {
          subField.value = "";
        }
        subField.modified_by = "";
      }
    });
  }
}

function updateContainerHighlight(container, data) {
  // We only use the verified status for highlighting the container
  // No need to check individual fields at this time
  if (data.verified && data.verified.value === "True") {
    container.style.border = "2px solid green";
    container.style.borderRadius = "8px";
  } else {
    container.style.border = "2px solid red";
    container.style.borderRadius = "8px";
  }
}

// Function to render conditional content based on selected option
// This function can be called recursively for nested multi_input_fields
function renderConditionalContent(
  selectedOption,
  parentField,
  parentContainer,
  parentKey,
  nestedPath = []
) {
  // Ensure parentContainer is provided
  if (!parentContainer) {
    console.error("parentContainer is required for renderConditionalContent");
    return;
  }

  // Determine which conditional content to show based on selected option
  let conditionalKey = null;
  if (selectedOption.startsWith("Yes") && parentField.if_yes) {
    conditionalKey = "if_yes";
  } else if (selectedOption === "No" && parentField.if_no) {
    conditionalKey = "if_no";
  } else if (selectedOption === "Alive" && parentField.if_alive) {
    conditionalKey = "if_alive";
  } else if (selectedOption === "Deceased" && parentField.if_deceased) {
    conditionalKey = "if_deceased";
  }

  // If no conditional content is available, return
  if (!conditionalKey || !parentField[conditionalKey]) {
    return;
  }

  // Show the container
  parentContainer.style.display = "block";

  // Render each sub-field in the conditional content
  Object.entries(parentField[conditionalKey]).forEach(([subKey, subField]) => {
    // Create a container for the sub-field
    const subFieldContainer = document.createElement("div");
    subFieldContainer.classList.add("sub-field-container");
    subFieldContainer.style.marginBottom = "15px";

    // Create a label for the sub-field
    const subLabel = document.createElement("label");
    subLabel.classList.add("label");
    subLabel.innerHTML = `${subField.label} ${
      subField.field_id ? `(${subField.field_id})` : ""
    }`;
    if (subField.description) {
      subLabel.setAttribute("title", subField.description);
    }
    subFieldContainer.appendChild(subLabel);

    // If a metric is available, insert it after the label
    if (subField.metric) {
      const metricLabel = document.createElement("span");
      metricLabel.classList.add("metric");
      metricLabel.textContent = ` (${subField.metric})`;
      subFieldContainer.appendChild(metricLabel);
    }

    // Process the sub-field based on its input type
    if (subField.input_type === "string" || subField.input_type === "text") {
      const input = document.createElement("input");
      input.type = "text";
      input.name = `${parentKey}-${subKey}`;
      input.placeholder = `Enter ${subField.label}`;
      input.value = subField.value || "";

      let previousValue = input.value;

      input.addEventListener("input", (e) => {
        const currentValue = e.target.value;

        // Use validation utilities with temporary callback
        let isValid = false;
        validateStringInput(
          currentValue,
          subField.field_id,
          (validatedValue) => {
            isValid = true;
            previousValue = validatedValue;

            // Update model and UI only if value is valid
            parentField[conditionalKey][subKey].value = validatedValue;
            parentField[conditionalKey][subKey].modified_by = "ABSTRACTOR";

            // Update input value if validation modified it
            if (validatedValue !== currentValue) {
              e.target.value = validatedValue;
            }
          }
        );

        // Revert to previous value if validation failed
        if (!isValid) {
          e.target.value = previousValue;
        }
      });

      subFieldContainer.appendChild(input);
    } else if (subField.input_type === "date") {
      // Create date input
      const dateWrapper = document.createElement("div");
      dateWrapper.style.position = "relative";

      // Create display input
      const displayInput = document.createElement("input");
      displayInput.type = "text";
      displayInput.name = `${parentKey}-${subKey}-display`;
      displayInput.readOnly = true;
      displayInput.value = formatDisplayDate(subField.value);
      displayInput.placeholder = "MM/DD/YYYY";
      displayInput.style.cursor = "pointer";

      // Hidden date input
      const dateInput = document.createElement("input");
      dateInput.type = "date";
      dateInput.name = `${parentKey}-${subKey}`;
      dateInput.value = subField.value || "";
      dateInput.style.position = "absolute";
      dateInput.style.opacity = "0";
      dateInput.style.cursor = "pointer";

      // Set max date to today
      const today = new Date().toISOString().split("T")[0];
      dateInput.max = today;

      dateInput.addEventListener("change", (e) => {
        const selectedDate = e.target.value;
        displayInput.value = formatDisplayDate(selectedDate);
        parentField[conditionalKey][subKey].value = selectedDate;
        parentField[conditionalKey][subKey].modified_by = "ABSTRACTOR";
      });

      // Trigger date picker when clicking display input
      displayInput.addEventListener("click", () => {
        dateInput.showPicker();
      });

      dateWrapper.appendChild(displayInput);
      dateWrapper.appendChild(dateInput);
      subFieldContainer.appendChild(dateWrapper);
    } else if (subField.input_type === "date_time") {
      const dateTimeWrapper = document.createElement("div");
      dateTimeWrapper.style.position = "relative";
      dateTimeWrapper.style.display = "flex";
      dateTimeWrapper.style.flexDirection = "column";
      dateTimeWrapper.style.gap = "8px";

      // Create display input for the combined date and time
      const displayInput = document.createElement("input");
      displayInput.type = "text";
      displayInput.name = `${parentKey}-${subKey}-display`;
      displayInput.readOnly = true;
      displayInput.value = formatDisplayDateTime(subField.value);
      displayInput.placeholder = "MM/DD/YYYY HH:MM AM/PM";
      displayInput.style.cursor = "pointer";

      // Create a container for the date and time pickers
      const pickersContainer = document.createElement("div");
      pickersContainer.style.display = "flex";
      pickersContainer.style.gap = "8px";
      pickersContainer.style.position = "absolute";
      pickersContainer.style.opacity = "0";
      pickersContainer.style.pointerEvents = "none";

      // Hidden date input
      const dateInput = document.createElement("input");
      dateInput.type = "date";
      dateInput.name = `${parentKey}-${subKey}-date`;
      dateInput.style.cursor = "pointer";

      // Hidden time input
      const timeInput = document.createElement("input");
      timeInput.type = "time";
      timeInput.name = `${parentKey}-${subKey}-time`;
      timeInput.style.cursor = "pointer";

      // Set initial values if available
      if (subField.value) {
        const dateObj = new Date(subField.value);
        if (!isNaN(dateObj.getTime())) {
          // Set date value (YYYY-MM-DD)
          dateInput.value = dateObj.toISOString().split("T")[0];

          // Set time value (HH:MM)
          const hours = String(dateObj.getHours()).padStart(2, "0");
          const minutes = String(dateObj.getMinutes()).padStart(2, "0");
          timeInput.value = `${hours}:${minutes}`;
        }
      }

      // Set max date to today
      const today = new Date().toISOString().split("T")[0];
      dateInput.max = today;

      // Function to update the combined date-time value
      const updateDateTime = () => {
        if (dateInput.value) {
          let dateTimeValue;

          if (timeInput.value) {
            // Combine date and time
            const [hours, minutes] = timeInput.value.split(":");
            const dateObj = new Date(dateInput.value);
            dateObj.setHours(parseInt(hours, 10));
            dateObj.setMinutes(parseInt(minutes, 10));
            dateTimeValue = dateObj.toISOString();
          } else {
            // Use just the date with time set to midnight
            dateTimeValue = `${dateInput.value}T00:00:00.000Z`;
          }

          // Update display and model
          displayInput.value = formatDisplayDateTime(dateTimeValue);
          parentField[conditionalKey][subKey].value = dateTimeValue;
          parentField[conditionalKey][subKey].modified_by = "ABSTRACTOR";
        }
      };

      // Event listeners for date and time inputs
      dateInput.addEventListener("change", updateDateTime);
      timeInput.addEventListener("change", updateDateTime);

      // Show date picker when clicking the display input
      displayInput.addEventListener("click", () => {
        // Make the pickers container visible temporarily
        pickersContainer.style.opacity = "1";
        pickersContainer.style.pointerEvents = "auto";
        pickersContainer.style.zIndex = "100";

        // Show the date picker first
        dateInput.showPicker();

        // After date is selected, show time picker
        dateInput.addEventListener(
          "change",
          function onDateSelected() {
            setTimeout(() => {
              timeInput.showPicker();
            }, 100);
            dateInput.removeEventListener("change", onDateSelected);
          },
          { once: true }
        );

        // Hide the pickers container after time is selected
        timeInput.addEventListener(
          "change",
          function onTimeSelected() {
            setTimeout(() => {
              pickersContainer.style.opacity = "0";
              pickersContainer.style.pointerEvents = "none";
            }, 500);
            timeInput.removeEventListener("change", onTimeSelected);
          },
          { once: true }
        );
      });

      // Add inputs to their containers
      pickersContainer.appendChild(dateInput);
      pickersContainer.appendChild(timeInput);
      dateTimeWrapper.appendChild(displayInput);
      dateTimeWrapper.appendChild(pickersContainer);
      subFieldContainer.appendChild(dateTimeWrapper);
    } else if (subField.input_type === "radio") {
      // Create radio buttons container
      const subRadioContainer = document.createElement("div");
      subRadioContainer.classList.add("radio-container");
      subRadioContainer.style.display = "flex";
      subRadioContainer.style.flexDirection = "row";
      subRadioContainer.style.flexWrap = "nowrap"; // Prevent wrapping to keep in one line
      subRadioContainer.style.gap = "15px";

      subField.options.forEach((option) => {
        const radioWrapper = document.createElement("div");
        radioWrapper.style.display = "flex";
        radioWrapper.style.alignItems = "center";

        const input = document.createElement("input");
        input.type = "radio";
        input.name = `${parentKey}-${subKey}`;
        input.value = option;
        input.id = `${parentKey}-${subKey}-${option}`;
        input.checked = subField.value === option;

        input.addEventListener("change", (e) => {
          if (e.target.checked) {
            // Update the sub-field value
            parentField[conditionalKey][subKey].value = e.target.value;
            parentField[conditionalKey][subKey].modified_by = "ABSTRACTOR";
          }
        });

        const optionLabel = document.createElement("label");
        optionLabel.setAttribute("for", `${parentKey}-${subKey}-${option}`);
        optionLabel.innerText = option;
        if (subField.description) {
          optionLabel.setAttribute("title", subField.description);
        }

        radioWrapper.appendChild(input);
        radioWrapper.appendChild(optionLabel);
        subRadioContainer.appendChild(radioWrapper);
      });

      subFieldContainer.appendChild(subRadioContainer);
    } else if (subField.input_type === "select") {
      // Create select dropdown
      const select = document.createElement("select");
      select.name = `${parentKey}-${subKey}`;

      // Add a default option if no value is set
      if (!subField.value) {
        const defaultOption = document.createElement("option");
        defaultOption.value = "";
        defaultOption.innerText = "Select an option";
        defaultOption.disabled = true;
        defaultOption.selected = true;
        if (subField.description) {
          defaultOption.setAttribute("title", subField.description);
        }
        select.appendChild(defaultOption);
      }

      // Add options to the select dropdown
      subField.options.forEach((option) => {
        const optionElement = document.createElement("option");
        optionElement.value = option.id;
        optionElement.innerText = option.value;
        if (subField.value && subField.value === option.value) {
          optionElement.selected = true;
        }
        select.appendChild(optionElement);
      });

      select.addEventListener("change", (e) => {
        const selOption = subField.options.find(
          (option) => option.id === e.target.value
        );
        const newValue = selOption ? selOption.value : "";
        parentField[conditionalKey][subKey].value = newValue;
        parentField[conditionalKey][subKey].modified_by = "ABSTRACTOR";
      });

      subFieldContainer.appendChild(select);
    } else if (subField.input_type === "multi_input_field") {
      // Handle nested multi_input_field
      const nestedMultiInputContainer = document.createElement("div");
      nestedMultiInputContainer.classList.add("multi-input-container");
      nestedMultiInputContainer.style.marginTop = "10px";

      // Create radio buttons for the nested multi_input_field
      const nestedRadioContainer = document.createElement("div");
      nestedRadioContainer.classList.add("radio-container");
      nestedRadioContainer.style.display = "flex";
      nestedRadioContainer.style.flexDirection = "row";
      nestedRadioContainer.style.flexWrap = "nowrap"; // Prevent wrapping to keep in one line
      nestedRadioContainer.style.gap = "15px";
      nestedRadioContainer.style.width = "100%";

      // Create a single container for nested conditional content
      const nestedConditionalContainer = document.createElement("div");
      nestedConditionalContainer.classList.add("conditional-content-container");
      nestedConditionalContainer.style.marginTop = "10px";
      nestedConditionalContainer.style.display = "none"; // Initially hidden

      // Create radio buttons for each option in the nested multi_input_field
      subField.options.forEach((option) => {
        const radioWrapper = document.createElement("div");
        radioWrapper.style.display = "flex";
        radioWrapper.style.alignItems = "center";

        const input = document.createElement("input");
        input.type = "radio";
        input.name = `${parentKey}-${subKey}`;
        input.value = option;
        input.id = `${parentKey}-${subKey}-${option}`;
        input.checked = subField.value === option;

        // Function to update the nested field value
        const updateNestedValue = (value) => {
          parentField[conditionalKey][subKey].value = value;
          parentField[conditionalKey][subKey].modified_by = "ABSTRACTOR";
        };

        // Update data model and render nested conditional content
        input.addEventListener("change", (e) => {
          if (e.target.checked) {
            // Update the nested field value
            updateNestedValue(e.target.value);

            // Clear values from other conditional sections when switching options
            if (e.target.value === "No" && subField.if_yes) {
              clearConditionalValues(subField, "if_yes");
            } else if (e.target.value === "Yes" && subField.if_no) {
              clearConditionalValues(subField, "if_no");
            } else if (e.target.value === "Deceased" && subField.if_alive) {
              clearConditionalValues(subField, "if_alive");
            } else if (e.target.value === "Alive" && subField.if_deceased) {
              clearConditionalValues(subField, "if_deceased");
            }

            // Always clear any previously rendered content first
            nestedConditionalContainer.innerHTML = "";
            nestedConditionalContainer.style.display = "none";

            // Determine which nested conditional content to show
            let nestedConditionalKey = null;
            if (option === "Yes" && subField.if_yes) {
              nestedConditionalKey = "if_yes";
            } else if (option === "No" && subField.if_no) {
              nestedConditionalKey = "if_no";
            } else if (option === "Alive" && subField.if_alive) {
              nestedConditionalKey = "if_alive";
            } else if (option === "Deceased" && subField.if_deceased) {
              nestedConditionalKey = "if_deceased";
            }

            // Render nested conditional content if available
            if (nestedConditionalKey && subField[nestedConditionalKey]) {
              // Create a new path for this nested level
              const newPath = [...nestedPath, { key: subKey, conditionalKey }];
              // Recursively render the nested conditional content
              renderConditionalContent(
                option,
                subField,
                nestedConditionalContainer,
                `${parentKey}-${subKey}`,
                newPath
              );
            }
          }
        });

        const optionLabel = document.createElement("label");
        optionLabel.setAttribute("for", `${parentKey}-${subKey}-${option}`);
        optionLabel.innerText = option;
        if (subField.description) {
          optionLabel.setAttribute("title", subField.description);
        }

        radioWrapper.appendChild(input);
        radioWrapper.appendChild(optionLabel);
        nestedRadioContainer.appendChild(radioWrapper);
      });

      // Add radio container and conditional container to the multi-input container
      nestedMultiInputContainer.appendChild(nestedRadioContainer);
      nestedMultiInputContainer.appendChild(nestedConditionalContainer);
      subFieldContainer.appendChild(nestedMultiInputContainer);

      // If a value is already selected, render the nested conditional content
      if (subField.value) {
        // Find the radio button for the selected value
        const selectedRadio = nestedRadioContainer.querySelector(
          `input[value="${subField.value}"]`
        );
        if (selectedRadio) {
          // Determine which nested conditional content to show
          let nestedConditionalKey = null;
          if (subField.value === "Yes" && subField.if_yes) {
            nestedConditionalKey = "if_yes";
          } else if (subField.value === "No" && subField.if_no) {
            nestedConditionalKey = "if_no";
          } else if (subField.value === "Alive" && subField.if_alive) {
            nestedConditionalKey = "if_alive";
          } else if (subField.value === "Deceased" && subField.if_deceased) {
            nestedConditionalKey = "if_deceased";
          }

          // Render nested conditional content if available
          if (nestedConditionalKey && subField[nestedConditionalKey]) {
            // Create a new path for this nested level
            const newPath = [...nestedPath, { key: subKey, conditionalKey }];
            // Recursively render the nested conditional content
            renderConditionalContent(
              subField.value,
              subField,
              nestedConditionalContainer,
              `${parentKey}-${subKey}`,
              newPath
            );
          }
        }
      }
    } else if (subField.input_type === "multi_select") {
      // Initialize the value as an array if it's not already
      if (!Array.isArray(subField.value)) {
        subField.value = subField.value ? [subField.value] : [];
      }

      // Create dropdown container with relative positioning
      const dropdownContainer = document.createElement("div");
      dropdownContainer.classList.add("dropdown-container");
      dropdownContainer.style.position = "relative";
      dropdownContainer.style.width = "100%";

      // Create dropdown header/button
      const dropdownHeader = document.createElement("div");
      dropdownHeader.classList.add("dropdown-header");
      dropdownHeader.style.padding = "8px 12px";
      dropdownHeader.style.border = "1px solid #ccc";
      dropdownHeader.style.borderRadius = "4px";
      dropdownHeader.style.cursor = "pointer";
      dropdownHeader.style.display = "flex";
      dropdownHeader.style.justifyContent = "space-between";
      dropdownHeader.style.alignItems = "center";
      dropdownHeader.style.backgroundColor = "#fff";

      // Display selected values or placeholder
      const selectedText = document.createElement("span");
      selectedText.classList.add("selected-text");

      // Function to update the selected text display
      const updateSelectedText = () => {
        if (subField.value.length === 0) {
          selectedText.textContent = "Select options...";
        } else if (subField.value.length === 1) {
          const selectedOption = subField.options.find(
            (opt) => opt.value === subField.value[0]
          );
          selectedText.textContent = selectedOption
            ? selectedOption.value
            : subField.value[0];
        } else {
          selectedText.textContent = `${subField.value.length} options selected`;
        }
      };

      updateSelectedText();

      // Add dropdown arrow
      const dropdownArrow = document.createElement("span");
      dropdownArrow.innerHTML = "&#9662;"; // Down arrow character

      dropdownHeader.appendChild(selectedText);
      dropdownHeader.appendChild(dropdownArrow);

      // Create dropdown content (initially hidden)
      const dropdownContent = document.createElement("div");
      dropdownContent.classList.add("dropdown-content");
      dropdownContent.style.display = "none";
      dropdownContent.style.position = "absolute";
      dropdownContent.style.top = "100%";
      dropdownContent.style.left = "0";
      dropdownContent.style.width = "100%";
      dropdownContent.style.maxHeight = "200px";
      dropdownContent.style.overflowY = "auto";
      dropdownContent.style.backgroundColor = "#fff";
      dropdownContent.style.border = "1px solid #ccc";
      dropdownContent.style.borderRadius = "4px";
      dropdownContent.style.boxShadow = "0 4px 8px rgba(0,0,0,0.1)";
      dropdownContent.style.zIndex = "1000";

      // Create a checkbox for each option
      subField.options.forEach((option) => {
        const checkboxWrapper = document.createElement("div");
        checkboxWrapper.classList.add("checkbox-wrapper");
        checkboxWrapper.style.display = "flex";
        checkboxWrapper.style.alignItems = "center";
        checkboxWrapper.style.padding = "8px 12px";
        checkboxWrapper.style.borderBottom = "1px solid #eee";

        const input = document.createElement("input");
        input.type = "checkbox";
        input.name = `${parentKey}-${subKey}-${option.id}`;
        input.value = option.id;
        input.id = `${parentKey}-${subKey}-${option.id}`;
        input.style.marginRight = "8px";

        // Check if this option is in the selected values array
        input.checked = subField.value.includes(option.value);

        input.addEventListener("change", (e) => {
          // Get the current values array
          let currentValues = Array.isArray(subField.value)
            ? [...subField.value]
            : [];

          if (e.target.checked) {
            // Add the value if it's not already in the array
            if (!currentValues.includes(option.value)) {
              currentValues.push(option.value);
            }
          } else {
            // Remove the value if it's in the array
            currentValues = currentValues.filter((val) => val !== option.value);
          }

          // Update the model
          parentField[conditionalKey][subKey].value = currentValues;
          parentField[conditionalKey][subKey].modified_by = "ABSTRACTOR";

          // Update selected text display
          updateSelectedText();
        });

        const optionLabel = document.createElement("label");
        optionLabel.setAttribute("for", `${parentKey}-${subKey}-${option.id}`);
        optionLabel.innerText = option.value;

        checkboxWrapper.appendChild(input);
        checkboxWrapper.appendChild(optionLabel);
        dropdownContent.appendChild(checkboxWrapper);
      });

      // Toggle dropdown on click
      dropdownHeader.addEventListener("click", () => {
        const isOpen = dropdownContent.style.display === "block";
        dropdownContent.style.display = isOpen ? "none" : "block";
        dropdownArrow.innerHTML = isOpen ? "&#9662;" : "&#9652;";
      });

      // Close dropdown when clicking outside
      document.addEventListener("click", (e) => {
        if (!dropdownContainer.contains(e.target)) {
          dropdownContent.style.display = "none";
          dropdownArrow.innerHTML = "&#9662;";
        }
      });

      dropdownContainer.appendChild(dropdownHeader);
      dropdownContainer.appendChild(dropdownContent);
      subFieldContainer.appendChild(dropdownContainer);
    }

    // Add the sub-field container to the parent container
    parentContainer.appendChild(subFieldContainer);
  });
}

// Function to render fields for a category
// Function to render access systems repeatable container
function renderAccessSystems(accessSystems, categoryContainer, procedureInfo) {
  // Create a container for the access systems
  const accessSystemsContainer = document.createElement("div");
  accessSystemsContainer.classList.add("access-systems-container");
  accessSystemsContainer.style.width = "100%";
  accessSystemsContainer.style.marginBottom = "20px";

  // Create a header for the access systems
  const accessSystemsHeader = document.createElement("div");
  accessSystemsHeader.classList.add("access-systems-header");
  accessSystemsHeader.style.display = "flex";
  accessSystemsHeader.style.justifyContent = "space-between";
  accessSystemsHeader.style.alignItems = "center";
  accessSystemsHeader.style.marginBottom = "10px";
  accessSystemsHeader.style.padding = "10px";
  accessSystemsHeader.style.backgroundColor = "#f5f5f5";
  accessSystemsHeader.style.borderRadius = "4px";

  // Create a label for the access systems
  const accessSystemsLabel = document.createElement("h5");
  accessSystemsLabel.textContent = `${accessSystems.label}`; // Remove field_id display
  accessSystemsLabel.style.margin = "0";
  accessSystemsLabel.style.fontWeight = "bold";

  // Create an add button for the access systems
  const addAccessSystemButton = document.createElement("button");
  addAccessSystemButton.textContent =
    accessSystems.add_button_text || "Add Access System";
  addAccessSystemButton.classList.add("btn", "btn-primary", "btn-sm");
  addAccessSystemButton.style.backgroundColor = "#8143d9";
  addAccessSystemButton.style.border = "none";
  addAccessSystemButton.style.padding = "5px 10px";
  addAccessSystemButton.style.borderRadius = "4px";
  addAccessSystemButton.style.color = "white";
  addAccessSystemButton.style.cursor = "pointer";

  // Add the label and button to the header
  accessSystemsHeader.appendChild(accessSystemsLabel);
  accessSystemsHeader.appendChild(addAccessSystemButton);

  // Add the header to the container
  accessSystemsContainer.appendChild(accessSystemsHeader);

  // Create a container for the access system items
  const accessSystemItemsContainer = document.createElement("div");
  accessSystemItemsContainer.classList.add("access-system-items");
  accessSystemItemsContainer.style.display = "flex";
  accessSystemItemsContainer.style.flexDirection = "column";
  accessSystemItemsContainer.style.gap = "20px";

  // Initialize the access systems array if it doesn't exist
  if (!procedureInfo.device_information.access_systems.items) {
    procedureInfo.device_information.access_systems.items = [];
  }

  // Function to render an access system item
  function renderAccessSystemItem(accessSystemItem, index) {
    // Create an outer wrapper to hold the field tile and the modified_by display
    const accessSystemItemWrapper = document.createElement("div");
    accessSystemItemWrapper.classList.add("tile-wrapper");
    accessSystemItemWrapper.style.position = "relative";
    accessSystemItemWrapper.style.marginBottom = "24px";
    accessSystemItemWrapper.style.width = "100%";

    // Create a container for the access system item with highlight feature
    const accessSystemItemContainer = document.createElement("div");
    accessSystemItemContainer.classList.add(
      "access-system-item",
      "field-container"
    );
    accessSystemItemContainer.style.border = "1px solid #ddd";
    accessSystemItemContainer.style.borderRadius = "4px";
    accessSystemItemContainer.style.padding = "15px";
    accessSystemItemContainer.style.backgroundColor = "#f9f9f9";
    accessSystemItemContainer.style.width = "100%";

    // Create the modified_by display element
    const modifiedByDisplay = document.createElement("span");
    modifiedByDisplay.classList.add("modified-by");
    modifiedByDisplay.style.display = "block";
    modifiedByDisplay.style.textAlign = "right";
    modifiedByDisplay.style.marginTop = "5px";
    modifiedByDisplay.style.color = "#8143d9";
    modifiedByDisplay.style.fontSize = "12px";
    modifiedByDisplay.style.position = "absolute";
    modifiedByDisplay.style.bottom = "0";
    modifiedByDisplay.style.right = "10px";

    // Check if any field in this access system has been modified or has a value
    let isModified = false;
    let hasValue = false;

    if (accessSystemItem.access_system_counter?.template?.access_system) {
      // Check for modified status
      if (
        accessSystemItem.access_system_counter.template.access_system
          .modified_by
      ) {
        isModified = true;
        modifiedByDisplay.textContent =
          accessSystemItem.access_system_counter.template.access_system.modified_by;
      }

      // Check if the access system has a value
      if (accessSystemItem.access_system_counter.template.access_system.value) {
        hasValue = true;
      }

      // Check if any device has a value or is modified
      if (
        accessSystemItem.access_system_counter.template.access_system.template
          ?.devices?.items
      ) {
        accessSystemItem.access_system_counter.template.access_system.template.devices.items.forEach(
          (deviceItem) => {
            if (deviceItem.device_counter?.template) {
              Object.values(deviceItem.device_counter.template).forEach(
                (field) => {
                  if (field && field.modified_by) {
                    isModified = true;
                    if (!modifiedByDisplay.textContent) {
                      modifiedByDisplay.textContent = field.modified_by;
                    }
                  }
                  if (field && field.value) {
                    hasValue = true;
                  }
                }
              );
            }
          }
        );
      }
    }

    // Apply highlight using the existing updateTileStyle function
    const value = hasValue ? "filled" : "";
    const modified = isModified ? "modified" : "";
    updateTileStyle(accessSystemItemContainer, value, modified);

    // Create a header for the access system item
    const accessSystemItemHeader = document.createElement("div");
    accessSystemItemHeader.classList.add("access-system-item-header");
    accessSystemItemHeader.style.display = "flex";
    accessSystemItemHeader.style.justifyContent = "space-between";
    accessSystemItemHeader.style.alignItems = "center";
    accessSystemItemHeader.style.marginBottom = "10px";

    // Create a label for the access system item with counter and field ID
    const accessSystemItemLabel = document.createElement("h6");
    accessSystemItemLabel.textContent = `Access System Counter (14840): ${
      index + 1
    } `;
    accessSystemItemLabel.style.margin = "0";
    accessSystemItemLabel.style.fontWeight = "bold";

    // Create a remove button for the access system item
    const removeAccessSystemButton = document.createElement("button");
    removeAccessSystemButton.textContent = "Remove";
    removeAccessSystemButton.classList.add("btn", "btn-danger", "btn-sm");
    removeAccessSystemButton.style.backgroundColor = "#dc3545";
    removeAccessSystemButton.style.border = "none";
    removeAccessSystemButton.style.padding = "3px 8px";
    removeAccessSystemButton.style.borderRadius = "4px";
    removeAccessSystemButton.style.color = "white";
    removeAccessSystemButton.style.cursor = "pointer";

    // Add event listener to remove button
    removeAccessSystemButton.addEventListener("click", () => {
      // Remove the access system item from the array
      procedureInfo.device_information.access_systems.items.splice(index, 1);

      // Re-render the access system items
      accessSystemItemsContainer.innerHTML = "";
      procedureInfo.device_information.access_systems.items.forEach(
        (item, idx) => {
          renderAccessSystemItem(item, idx);
        }
      );
    });

    // Add the label and button to the header
    accessSystemItemHeader.appendChild(accessSystemItemLabel);
    accessSystemItemHeader.appendChild(removeAccessSystemButton);

    // Add the header to the container
    accessSystemItemContainer.appendChild(accessSystemItemHeader);

    // Create a form for the access system fields
    const accessSystemForm = document.createElement("div");
    accessSystemForm.classList.add("access-system-form");
    accessSystemForm.style.display = "flex";
    accessSystemForm.style.flexDirection = "column";
    accessSystemForm.style.gap = "15px";

    // Create the access system name field
    const accessSystemNameContainer = document.createElement("div");
    accessSystemNameContainer.classList.add("field-container");

    // Add highlight feature to the field container
    if (
      accessSystemItem.access_system_counter?.template?.access_system?.value
    ) {
      accessSystemNameContainer.style.borderLeft = "4px solid #8143d9";
      accessSystemNameContainer.style.paddingLeft = "10px";
    }

    // Create a label for the access system name
    const accessSystemNameLabel = document.createElement("label");
    accessSystemNameLabel.textContent = "Access System Name";
    accessSystemNameLabel.style.fontWeight = "bold";
    accessSystemNameLabel.style.marginBottom = "5px";
    accessSystemNameLabel.style.display = "block";

    // Initialize the access system field structure
    if (!accessSystemItem.access_system_counter) {
      accessSystemItem.access_system_counter = {
        field_id: "14840",
        label: "Access System counter",
        input_type: "string",
        value: (index + 1).toString(),
        template: {},
      };
    }

    if (!accessSystemItem.access_system_counter.template) {
      accessSystemItem.access_system_counter.template = {};
    }

    if (!accessSystemItem.access_system_counter.template.access_system) {
      accessSystemItem.access_system_counter.template.access_system = {
        field_id: "14839",
        label: "Access System Name",
        input_type: "select",
        value: "",
        options: [
          { id: "1", value: "WATCHMAN Single Curve 14F Access System" },
          { id: "2", value: "WATCHMAN Double Curve 14F Access System" },
          { id: "3", value: "WATCHMAN Anterior Curve 14F Access System" },
          { id: "4", value: "Amplatzer TorqVue 45/60" },
          { id: "5", value: "Amplatzer TorqVue 45/80" },
          { id: "6", value: "Amplatzer TorqVue 180/60" },
          { id: "7", value: "Amplatzer TorqVue 180/80" },
          { id: "8", value: "FindrWIRZ .025 inch Magnet-tipped guide" },
          { id: "9", value: "FindrWIRZ .035 inch Magnet-tipped guide" },
          { id: "10", value: "Amplatzer TorqVue LP 90/80" },
          { id: "11", value: "CARDIOFORM 10F Access System" },
          { id: "12", value: "Amplatzer Vascular Plug Delivery System" },
          { id: "13", value: "WATCHMAN TruSeal Single Curve 14F Access System" },
          { id: "14", value: "WATCHMAN TruSeal Double Curve 14F Access System" },
          { id: "15", value: "WATCHMAN TruSeal Anterior Curve 14F Access System" },
          { id: "16", value: "Amplatzer TorqVue 45/45" },
          { id: "17", value: "Watchman FXD Single Curve 15F Access System" },
          { id: "18", value: "Watchman FXD Double Curve 15F Access System" },
          { id: "19", value: "Amplatzer Steerable Delivery Sheath" },
          { id: "20", value: "Cardioform ASD Closure Delivery Sheath" },
          { id: "21", value: "Agilis NxT Steerable Introducer Sheath - Large Curl/Dual Reach 71 cm" },
          { id: "22", value: "Amplatzer Trevisio Intravascular Delivery System" },
          { id: "23", value: "8 Fr Amplatzer Talisman Delivery Sheath" },
          { id: "24", value: "9 Fr Amplatzer Talisman Delivery Sheath" },
          { id: "25", value: "WATCHMAN TruSteer Access System" },
          { id: "26", value: "Amulet Steerable Delivery Sheath" },
          { id: "27", value: "Multiflex Steerable Sheath" }
        ]
      };
    }

    // Determine if we should render as select or text input based on field configuration
    const accessSystemField = accessSystemItem.access_system_counter.template.access_system;
    let accessSystemInput;

    if (accessSystemField.input_type === "select" && accessSystemField.options) {
      // Create select dropdown for access system name
      accessSystemInput = document.createElement("select");
      accessSystemInput.name = "access-system-name";
      accessSystemInput.style.width = "100%";
      accessSystemInput.style.padding = "8px";
      accessSystemInput.style.borderRadius = "4px";
      accessSystemInput.style.border = "1px solid #ddd";

      // Add a default option if no value is set
      if (!accessSystemField.value) {
        const defaultOption = document.createElement("option");
        defaultOption.value = "";
        defaultOption.innerText = "Select Access System";
        defaultOption.disabled = true;
        defaultOption.selected = true;
        accessSystemInput.appendChild(defaultOption);
      }

      // Add options to the select dropdown
      accessSystemField.options.forEach((option) => {
        const optionElement = document.createElement("option");
        optionElement.value = option.id;
        optionElement.innerText = option.value;
        if (accessSystemField.value && accessSystemField.value === option.value) {
          optionElement.selected = true;
        }
        accessSystemInput.appendChild(optionElement);
      });

      // Add event listener for select change
      accessSystemInput.addEventListener("change", (e) => {
        const selectedOption = accessSystemField.options.find(
          (option) => option.id === e.target.value
        );
        const newValue = selectedOption ? selectedOption.value : "";

        accessSystemField.value = newValue;
        accessSystemField.modified_by = "ABSTRACTOR";

        // Update the modified_by display
        modifiedByDisplay.textContent = "ABSTRACTOR";

        // Update the highlight using the existing updateTileStyle function
        const hasValue = newValue ? true : false;

        // Check if any device has a value if this field is empty
        let hasAnyValue = hasValue;
        if (!hasValue) {
          if (
            accessSystemItem.access_system_counter.template.access_system.template
              ?.devices?.items
          ) {
            accessSystemItem.access_system_counter.template.access_system.template.devices.items.forEach(
              (deviceItem) => {
                if (deviceItem.device_counter?.template) {
                  Object.values(deviceItem.device_counter.template).forEach(
                    (field) => {
                      if (field && field.value) {
                        hasAnyValue = true;
                      }
                    }
                  );
                }
              }
            );
          }
        }

        // Update the tile style
        updateTileStyle(
          accessSystemItemContainer,
          hasAnyValue ? "filled" : "",
          "modified"
        );
      });
    } else {
      // Create text input for access system name (fallback)
      accessSystemInput = document.createElement("input");
      accessSystemInput.type = "text";
      accessSystemInput.placeholder = "Enter Access System Name";
      accessSystemInput.value = accessSystemField.value || "";
      accessSystemInput.style.width = "100%";
      accessSystemInput.style.padding = "8px";
      accessSystemInput.style.borderRadius = "4px";
      accessSystemInput.style.border = "1px solid #ddd";

      // Add event listener for text input
      accessSystemInput.addEventListener("input", (e) => {
        accessSystemField.value = e.target.value;
        accessSystemField.modified_by = "ABSTRACTOR";

        // Update the modified_by display
        modifiedByDisplay.textContent = "ABSTRACTOR";

        // Update the highlight using the existing updateTileStyle function
        const hasValue = e.target.value ? true : false;

        // Check if any device has a value if this field is empty
        let hasAnyValue = hasValue;
        if (!hasValue) {
          if (
            accessSystemItem.access_system_counter.template.access_system.template
              ?.devices?.items
          ) {
            accessSystemItem.access_system_counter.template.access_system.template.devices.items.forEach(
              (deviceItem) => {
                if (deviceItem.device_counter?.template) {
                  Object.values(deviceItem.device_counter.template).forEach(
                    (field) => {
                      if (field && field.value) {
                        hasAnyValue = true;
                      }
                    }
                  );
                }
              }
            );
          }
        }

        // Update the tile style
        updateTileStyle(
          accessSystemItemContainer,
          hasAnyValue ? "filled" : "",
          "modified"
        );
      });
    }

    // Add the label and input to the container
    accessSystemNameContainer.appendChild(accessSystemNameLabel);
    accessSystemNameContainer.appendChild(accessSystemInput);

    // Add the access system name container to the form
    accessSystemForm.appendChild(accessSystemNameContainer);

    // Create the devices container
    const devicesContainer = document.createElement("div");
    devicesContainer.classList.add("devices-container");
    devicesContainer.style.marginTop = "15px";

    // Create a header for the devices
    const devicesHeader = document.createElement("div");
    devicesHeader.classList.add("devices-header");
    devicesHeader.style.display = "flex";
    devicesHeader.style.justifyContent = "space-between";
    devicesHeader.style.alignItems = "center";
    devicesHeader.style.marginBottom = "10px";
    devicesHeader.style.padding = "8px";
    devicesHeader.style.backgroundColor = "#e9ecef";
    devicesHeader.style.borderRadius = "4px";

    // Create a label for the devices
    const devicesLabel = document.createElement("h6");
    devicesLabel.textContent = "Devices";
    devicesLabel.style.margin = "0";
    devicesLabel.style.fontWeight = "bold";

    // Create an add button for the devices
    const addDeviceButton = document.createElement("button");
    addDeviceButton.textContent = "Add Device";
    addDeviceButton.classList.add("btn", "btn-primary", "btn-sm");
    addDeviceButton.style.backgroundColor = "#8143d9";
    addDeviceButton.style.border = "none";
    addDeviceButton.style.padding = "3px 8px";
    addDeviceButton.style.borderRadius = "4px";
    addDeviceButton.style.color = "white";
    addDeviceButton.style.cursor = "pointer";

    // Add event listener to add device button
    addDeviceButton.addEventListener("click", () => {
      // Initialize the access_system_counter if it doesn't exist
      if (!accessSystemItem.access_system_counter) {
        accessSystemItem.access_system_counter = {
          field_id: "14840",
          label: "Access System counter",
          input_type: "string",
          value: (index + 1).toString(),
          template: {},
        };
      }

      // Initialize the template if it doesn't exist
      if (!accessSystemItem.access_system_counter.template) {
        accessSystemItem.access_system_counter.template = {};
      }

      // Initialize the access_system if it doesn't exist
      if (!accessSystemItem.access_system_counter.template.access_system) {
        accessSystemItem.access_system_counter.template.access_system = {
          field_id: "14839",
          label: "Access System Name",
          input_type: "select",
          value: "",
          options: [
            { id: "1", value: "WATCHMAN Single Curve 14F Access System" },
            { id: "2", value: "WATCHMAN Double Curve 14F Access System" },
            { id: "3", value: "WATCHMAN Anterior Curve 14F Access System" },
            { id: "4", value: "Amplatzer TorqVue 45/60" },
            { id: "5", value: "Amplatzer TorqVue 45/80" },
            { id: "6", value: "Amplatzer TorqVue 180/60" },
            { id: "7", value: "Amplatzer TorqVue 180/80" },
            { id: "8", value: "FindrWIRZ .025 inch Magnet-tipped guide" },
            { id: "9", value: "FindrWIRZ .035 inch Magnet-tipped guide" },
            { id: "10", value: "Amplatzer TorqVue LP 90/80" },
            { id: "11", value: "CARDIOFORM 10F Access System" },
            { id: "12", value: "Amplatzer Vascular Plug Delivery System" },
            { id: "13", value: "WATCHMAN TruSeal Single Curve 14F Access System" },
            { id: "14", value: "WATCHMAN TruSeal Double Curve 14F Access System" },
            { id: "15", value: "WATCHMAN TruSeal Anterior Curve 14F Access System" },
            { id: "16", value: "Amplatzer TorqVue 45/45" },
            { id: "17", value: "Watchman FXD Single Curve 15F Access System" },
            { id: "18", value: "Watchman FXD Double Curve 15F Access System" },
            { id: "19", value: "Amplatzer Steerable Delivery Sheath" },
            { id: "20", value: "Cardioform ASD Closure Delivery Sheath" },
            { id: "21", value: "Agilis NxT Steerable Introducer Sheath - Large Curl/Dual Reach 71 cm" },
            { id: "22", value: "Amplatzer Trevisio Intravascular Delivery System" },
            { id: "23", value: "8 Fr Amplatzer Talisman Delivery Sheath" },
            { id: "24", value: "9 Fr Amplatzer Talisman Delivery Sheath" },
            { id: "25", value: "WATCHMAN TruSteer Access System" },
            { id: "26", value: "Amulet Steerable Delivery Sheath" },
            { id: "27", value: "Multiflex Steerable Sheath" }
          ],
          template: {},
        };
      }

      // Initialize the template if it doesn't exist
      if (
        !accessSystemItem.access_system_counter.template.access_system.template
      ) {
        accessSystemItem.access_system_counter.template.access_system.template =
          {};
      }

      if (
        !accessSystemItem.access_system_counter.template.access_system.template
          .devices
      ) {
        accessSystemItem.access_system_counter.template.access_system.template.devices =
          {
            field_id: "14841_container",
            label: "Devices",
            input_type: "repeatable_container",
            add_button_text: "Add Device",
            value: "",
            items: [],
          };
      }

      if (
        !accessSystemItem.access_system_counter.template.access_system.template
          .devices.items
      ) {
        accessSystemItem.access_system_counter.template.access_system.template.devices.items =
          [];
      }

      // Add a new device item
      accessSystemItem.access_system_counter.template.access_system.template.devices.items.push(
        {
          device_counter: {
            field_id: "14842",
            label: "Device counter",
            input_type: "string",
            value: (
              accessSystemItem.access_system_counter.template.access_system
                .template.devices.items.length + 1
            ).toString(),
            template: {},
          },
        }
      );

      // Re-render the devices
      renderDevices();
    });

    // Add the label and button to the header
    devicesHeader.appendChild(devicesLabel);
    devicesHeader.appendChild(addDeviceButton);

    // Add the header to the container
    devicesContainer.appendChild(devicesHeader);

    // Create a container for the device items
    const deviceItemsContainer = document.createElement("div");
    deviceItemsContainer.classList.add("device-items");
    deviceItemsContainer.style.display = "block"; // Use block instead of flex for the container
    deviceItemsContainer.style.width = "100%";
    deviceItemsContainer.style.gap = "15px";

    // Function to render the devices
    function renderDevices() {
      // Clear the device items container
      deviceItemsContainer.innerHTML = "";

      // Check if devices exist
      if (
        !accessSystemItem.access_system_counter?.template?.access_system
          ?.template?.devices?.items
      ) {
        return;
      }

      // Render each device item
      accessSystemItem.access_system_counter.template.access_system.template.devices.items.forEach(
        (deviceItem, deviceIndex) => {
          // Create an outer wrapper to hold the device tile and the modified_by display
          const deviceItemWrapper = document.createElement("div");
          deviceItemWrapper.classList.add("tile-wrapper");
          deviceItemWrapper.style.position = "relative";
          deviceItemWrapper.style.marginBottom = "24px";
          deviceItemWrapper.style.width = "100%";

          // Create a container for the device item with highlight feature
          const deviceItemContainer = document.createElement("div");
          deviceItemContainer.classList.add("device-item", "field-container");
          deviceItemContainer.style.border = "1px solid #ddd";
          deviceItemContainer.style.borderRadius = "4px";
          deviceItemContainer.style.padding = "12px";
          deviceItemContainer.style.backgroundColor = "#fff";
          deviceItemContainer.style.width = "100%";
          deviceItemContainer.style.display = "block"; // Force block display
          deviceItemContainer.style.boxSizing = "border-box"; // Include padding in width calculation

          // Create the modified_by display element for the device
          const deviceModifiedByDisplay = document.createElement("span");
          deviceModifiedByDisplay.classList.add("modified-by");
          deviceModifiedByDisplay.style.display = "block";
          deviceModifiedByDisplay.style.textAlign = "right";
          deviceModifiedByDisplay.style.marginTop = "5px";
          deviceModifiedByDisplay.style.color = "#8143d9";
          deviceModifiedByDisplay.style.fontSize = "12px";
          deviceModifiedByDisplay.style.position = "absolute";
          deviceModifiedByDisplay.style.bottom = "0";
          deviceModifiedByDisplay.style.right = "10px";

          // Check if any field in this device has been modified or has a value
          let isDeviceModified = false;
          let hasDeviceValue = false;
          const deviceTemplate = deviceItem.device_counter.template;

          if (deviceTemplate) {
            Object.values(deviceTemplate).forEach((field) => {
              // Check for modified status
              if (field && field.modified_by) {
                isDeviceModified = true;
                deviceModifiedByDisplay.textContent = field.modified_by;
              }

              // Check if the field has a value
              if (field && field.value) {
                hasDeviceValue = true;
              }

              // Check nested fields (for multi_input_field)
              if (field && field.if_yes) {
                Object.values(field.if_yes).forEach((nestedField) => {
                  if (nestedField && nestedField.modified_by) {
                    isDeviceModified = true;
                    if (!deviceModifiedByDisplay.textContent) {
                      deviceModifiedByDisplay.textContent =
                        nestedField.modified_by;
                    }
                  }
                  if (nestedField && nestedField.value) {
                    hasDeviceValue = true;
                  }
                });
              }
            });
          }

          // Apply highlight using the existing updateTileStyle function
          const value = hasDeviceValue ? "filled" : "";
          const modified = isDeviceModified ? "modified" : "";
          updateTileStyle(deviceItemContainer, value, modified);

          // Create a header for the device item
          const deviceItemHeader = document.createElement("div");
          deviceItemHeader.classList.add("device-item-header");
          deviceItemHeader.style.display = "flex";
          deviceItemHeader.style.justifyContent = "space-between";
          deviceItemHeader.style.alignItems = "center";
          deviceItemHeader.style.marginBottom = "10px";
          deviceItemHeader.style.width = "100%";
          deviceItemHeader.style.gridColumn = "1 / -1"; // Make it span the full width

          // Create a label for the device item with counter and field ID
          const deviceItemLabel = document.createElement("h6");
          deviceItemLabel.textContent = `Device Counter (14842): ${
            deviceIndex + 1
          } `;
          deviceItemLabel.style.margin = "0";
          deviceItemLabel.style.fontWeight = "bold";

          // Create a remove button for the device item
          const removeDeviceButton = document.createElement("button");
          removeDeviceButton.textContent = "Remove";
          removeDeviceButton.classList.add("btn", "btn-danger", "btn-sm");
          removeDeviceButton.style.backgroundColor = "#dc3545";
          removeDeviceButton.style.border = "none";
          removeDeviceButton.style.padding = "2px 6px";
          removeDeviceButton.style.borderRadius = "4px";
          removeDeviceButton.style.color = "white";
          removeDeviceButton.style.cursor = "pointer";

          // Add event listener to remove button
          removeDeviceButton.addEventListener("click", () => {
            // Remove the device item from the array
            accessSystemItem.access_system_counter.template.access_system.template.devices.items.splice(
              deviceIndex,
              1
            );

            // Re-render the devices
            renderDevices();
          });

          // Add the label and button to the header
          deviceItemHeader.appendChild(deviceItemLabel);
          deviceItemHeader.appendChild(removeDeviceButton);

          // Add the header to the container
          deviceItemContainer.appendChild(deviceItemHeader);

          // Create a form for the device fields
          const deviceForm = document.createElement("div");
          deviceForm.classList.add("device-form");
          deviceForm.style.display = "flex";
          deviceForm.style.flexWrap = "wrap";
          deviceForm.style.gap = "15px";
          deviceForm.style.width = "100%";
          deviceForm.style.marginTop = "15px";
          deviceForm.style.gridColumn = "1 / -1"; // Override any parent grid settings
          deviceForm.style.gridRow = "auto"; // Override any parent grid settings
          deviceForm.style.position = "relative"; // Create a new positioning context

          // Initialize the device template if it doesn't exist
          if (!deviceItem.device_counter.template) {
            deviceItem.device_counter.template = {};
          }

          // Render the device fields
          const deviceFields = [
            {
              key: "device",
              label: "Device",
              type: "select",
              options: [
                { id: "1", value: "21mm WATCHMAN LAA Closure Device", manufacturer: "Boston Scientific" },
                { id: "2", value: "24mm WATCHMAN LAA Closure Device", manufacturer: "Boston Scientific" },
                { id: "3", value: "27mm WATCHMAN LAA Closure Device", manufacturer: "Boston Scientific" },
                { id: "4", value: "30mm WATCHMAN LAA Closure Device", manufacturer: "Boston Scientific" },
                { id: "5", value: "33mm WATCHMAN LAA Closure Device", manufacturer: "Boston Scientific" },
                { id: "6", value: "20 mm Amplatzer Septal Occluder", manufacturer: "St. Jude Medical" },
                { id: "7", value: "22 mm Amplatzer Septal Occluder", manufacturer: "St. Jude Medical" },
                { id: "8", value: "24 mm Amplatzer Septal Occluder", manufacturer: "St. Jude Medical" },
                { id: "9", value: "26 mm Amplatzer Septal Occluder", manufacturer: "St. Jude Medical" },
                { id: "10", value: "30 mm Amplatzer Septal Occluder", manufacturer: "St. Jude Medical" },
                { id: "11", value: "32 mm Amplatzer Septal Occluder", manufacturer: "St. Jude Medical" },
                { id: "12", value: "34 mm Amplatzer Septal Occluder", manufacturer: "St. Jude Medical" },
                { id: "13", value: "36 mm Amplatzer Septal Occluder", manufacturer: "St. Jude Medical" },
                { id: "14", value: "38 mm Amplatzer Septal Occluder", manufacturer: "St. Jude Medical" },
                { id: "15", value: "LARIAT Suture Delivery Device W 40 mm X H 20 mm X L 70 mm (30-02)", manufacturer: "SentreHEART" },
                { id: "16", value: "LARIAT + Suture Delivery Device W 45 mm X H 20 mm X L 70 mm (30-05)", manufacturer: "SentreHEART" },
                { id: "17", value: "LARIAT + Suture Delivery Device W 45 mm X H 20 mm X L 70 mm (90-01)", manufacturer: "SentreHEART" },
                { id: "18", value: "7 mm Amplatzer Septal Occluder", manufacturer: "St. Jude Medical" },
                { id: "19", value: "8 mm Amplatzer Septal Occluder", manufacturer: "St. Jude Medical" },
                { id: "20", value: "9 mm Amplatzer Septal Occluder", manufacturer: "St. Jude Medical" },
                { id: "21", value: "10 mm Amplatzer Septal Occluder", manufacturer: "St. Jude Medical" },
                { id: "22", value: "11 mm Amplatzer Septal Occluder", manufacturer: "St. Jude Medical" },
                { id: "23", value: "12 mm Amplatzer Septal Occluder", manufacturer: "St. Jude Medical" },
                { id: "24", value: "13 mm Amplatzer Septal Occluder", manufacturer: "St. Jude Medical" },
                { id: "25", value: "14 mm Amplatzer Septal Occluder", manufacturer: "St. Jude Medical" },
                { id: "26", value: "15 mm Amplatzer Septal Occluder", manufacturer: "St. Jude Medical" },
                { id: "27", value: "16 mm Amplatzer Septal Occluder", manufacturer: "St. Jude Medical" },
                { id: "28", value: "17 mm Amplatzer Septal Occluder", manufacturer: "St. Jude Medical" },
                { id: "29", value: "18 mm Amplatzer Septal Occluder", manufacturer: "St. Jude Medical" },
                { id: "30", value: "19 mm Amplatzer Septal Occluder", manufacturer: "St. Jude Medical" },
                { id: "31", value: "4 mm Amplatzer Septal Occluder", manufacturer: "St. Jude Medical" },
                { id: "32", value: "5 mm Amplatzer Septal Occluder", manufacturer: "St. Jude Medical" },
                { id: "33", value: "6 mm Amplatzer Septal Occluder", manufacturer: "St. Jude Medical" },
                { id: "34", value: "6 mm Amplatzer Duct Occluder II", manufacturer: "St. Jude Medical" },
                { id: "35", value: "Lariat RS Suture Delivery Device W 45 mm X H 20 mm X L 70 mm (30-06)", manufacturer: "SentreHEART" },
                { id: "36", value: "20 mm CARDIOFORM Septal Occluder", manufacturer: "GORE" },
                { id: "37", value: "25 mm CARDIOFORM Septal Occluder", manufacturer: "GORE" },
                { id: "38", value: "30 mm CARDIOFORM Septal Occluder", manufacturer: "GORE" },
                { id: "39", value: "14 mm Amplatzer Vascular Plug II", manufacturer: "Abbott" },
                { id: "40", value: "12 mm Amplatzer Vascular Plug II", manufacturer: "Abbott" },
                { id: "41", value: "3 mm Amplatzer Duct Occluder II", manufacturer: "St. Jude Medical" },
                { id: "42", value: "5 mm Amplatzer Duct Occluder II", manufacturer: "St. Jude Medical" },
                { id: "43", value: "16 mm Amplatzer Vascular Plug II", manufacturer: "Abbott" },
                { id: "44", value: "25 mm Amplatzer Multifenestrated Septal Occluder - Cribriform", manufacturer: "St. Jude Medical" },
                { id: "45", value: "10 mm Amplatzer Vascular Plug II", manufacturer: "Abbott" },
                { id: "46", value: "20 mm WATCHMAN FLX LAA Closure Device", manufacturer: "Boston Scientific" },
                { id: "47", value: "24 mm WATCHMAN FLX LAA Closure Device", manufacturer: "Boston Scientific" },
                { id: "48", value: "27 mm WATCHMAN FLX LAA Closure Device", manufacturer: "Boston Scientific" },
                { id: "49", value: "31 mm WATCHMAN FLX LAA Closure Device", manufacturer: "Boston Scientific" },
                { id: "50", value: "35 mm WATCHMAN FLX LAA Closure Device", manufacturer: "Boston Scientific" },
                { id: "51", value: "25 mm Amplatzer PFO Occluder", manufacturer: "Abbott" },
                { id: "52", value: "4 mm Amplatzer Duct Occluder II", manufacturer: "St. Jude Medical" },
                { id: "53", value: "Amplatzer Amulet 16 mm", manufacturer: "Abbott" },
                { id: "54", value: "Amplatzer Amulet 18 mm", manufacturer: "Abbott" },
                { id: "55", value: "Amplatzer Amulet 20 mm", manufacturer: "Abbott" },
                { id: "56", value: "Amplatzer Amulet 22 mm", manufacturer: "Abbott" },
                { id: "57", value: "Amplatzer Amulet 25 mm", manufacturer: "Abbott" },
                { id: "58", value: "Amplatzer Amulet 28 mm", manufacturer: "Abbott" },
                { id: "59", value: "Amplatzer Amulet 31 mm", manufacturer: "Abbott" },
                { id: "60", value: "Amplatzer Amulet 34 mm", manufacturer: "Abbott" },
                { id: "61", value: "35 mm Amplatzer PFO Occluder", manufacturer: "Abbott" },
                { id: "62", value: "8 mm Amplatzer Vascular Plug 4", manufacturer: "Abbott" },
                { id: "63", value: "18 mm Amplatzer PFO Occluder", manufacturer: "Abbott" },
                { id: "64", value: "8mm Amplatzer Vascular Plug II", manufacturer: "Abbott" },
                { id: "65", value: "27 mm Cardioform ASD Occluder", manufacturer: "GORE" },
                { id: "66", value: "32 mm Cardioform ASD Occluder", manufacturer: "GORE" },
                { id: "67", value: "37 mm Cardioform ASD Occluder", manufacturer: "GORE" },
                { id: "68", value: "44 mm Cardioform ASD Occluder", manufacturer: "GORE" },
                { id: "69", value: "48 mm Cardioform ASD Occluder", manufacturer: "GORE" },
                { id: "70", value: "6 mm Amplatzer Vascular Plug II", manufacturer: "Abbott" },
                { id: "71", value: "30 mm Amplatzer Talisman PFO Occluder", manufacturer: "Abbott" },
                { id: "72", value: "18 mm Amplatzer Talisman PFO Occluder", manufacturer: "Abbott" },
                { id: "73", value: "25 mm Amplatzer Talisman PFO Occluder", manufacturer: "Abbott" },
                { id: "74", value: "35 mm Amplatzer Talisman PFO Occluder", manufacturer: "Abbott" },
                { id: "75", value: "3 mm Amplatzer Vascular Plug II", manufacturer: "Abbott" },
                { id: "76", value: "4 mm Amplatzer Vascular Plug II", manufacturer: "Abbott" },
                { id: "77", value: "18 mm Amplatzer Vascular Plug II", manufacturer: "Abbott" },
                { id: "78", value: "20 mm Amplatzer Vascular Plug II", manufacturer: "Abbott" },
                { id: "79", value: "22 mm Amplatzer Vascular Plug II", manufacturer: "Abbott" },
                { id: "80", value: "4 mm Amplatzer Muscular VSD Occluder", manufacturer: "Abbott" },
                { id: "81", value: "20 mm WATCHMAN FLX-PRO LAA Closure Device", manufacturer: "Boston Scientific" },
                { id: "82", value: "24 mm WATCHMAN FLX-PRO LAA Closure Device", manufacturer: "Boston Scientific" },
                { id: "83", value: "27 mm WATCHMAN FLX-PRO LAA Closure Device", manufacturer: "Boston Scientific" },
                { id: "84", value: "31 mm WATCHMAN FLX-PRO LAA Closure Device", manufacturer: "Boston Scientific" },
                { id: "85", value: "35 mm WATCHMAN FLX-PRO LAA Closure Device", manufacturer: "Boston Scientific" },
                { id: "86", value: "40 mm WATCHMAN FLX-PRO LAA Closure Device", manufacturer: "Boston Scientific" },
                { id: "87", value: "8 mm Amplatzer Muscular VSD Occluder", manufacturer: "Abbott" },
                { id: "88", value: "6 mm Amplatzer Muscular VSD Occluder", manufacturer: "Abbott" },
                { id: "89", value: "10 mm Amplatzer Muscular VSD Occluder", manufacturer: "Abbott" },
                { id: "90", value: "12 mm Amplatzer Muscular VSD Occluder", manufacturer: "Abbott" },
                { id: "91", value: "14 mm Amplatzer Muscular VSD Occluder", manufacturer: "Abbott" },
                { id: "92", value: "16 mm Amplatzer Muscular VSD Occluder", manufacturer: "Abbott" },
                { id: "93", value: "18 mm Amplatzer Muscular VSD Occluder", manufacturer: "Abbott" },
                { id: "94", value: "7 mm Amplatzer Vascular Plug 4", manufacturer: "Abbott" },
                { id: "95", value: "4 mm Amplatzer Vascular Plug 4", manufacturer: "Abbott" },
                { id: "96", value: "5 mm Amplatzer Vascular Plug 4", manufacturer: "Abbott" },
                { id: "97", value: "6 mm Amplatzer Vascular Plug 4", manufacturer: "Abbott" }
              ]
            },
            {
              key: "udi",
              label: "UDI",
              type: "text",
              placeholder: "Enter UDI",
            },
            {
              key: "laa_isolation_approach",
              label: "LAA Isolation Approach",
              type: "radio",
              options: ["Epicardial", "Percutaneous"],
            },
            {
              key: "device_successfully_deployed",
              label: "Device Successfully Deployed",
              type: "radio",
              options: ["No", "Yes"],
              conditional: true,
              conditionalKey: "reason_device_not_deployed_successfully",
              conditionalLabel: "Reason Device Not Deployed Successfully",
              conditionalType: "select",
              conditionalOptions: [
                { id: "1", value: "Deployed, not released" },
                { id: "2", value: "Not deployed" },
                { id: "3", value: "Device retrieved" },
              ],
            },
          ];

          // Render each device field
          deviceFields.forEach((field) => {
            // Create a container for the field
            const fieldContainer = document.createElement("div");
            fieldContainer.classList.add("field-container");

            // Set width based on field type
            if (field.type === "radio" || field.conditional) {
              fieldContainer.style.width = "100%"; // Full width for radio and conditional fields
              fieldContainer.style.flexBasis = "100%"; // Ensure it takes full width in flex layout
            } else {
              fieldContainer.style.width = "calc(33.33% - 10px)"; // One-third width for other fields
              fieldContainer.style.minWidth = "250px"; // Minimum width to prevent too narrow fields
              fieldContainer.style.flexGrow = "1"; // Allow growing if space available
              fieldContainer.style.flexBasis = "calc(33.33% - 10px)"; // Set flex-basis to match width
            }

            // Override any grid settings from parent containers
            fieldContainer.style.gridColumn = "auto";
            fieldContainer.style.gridRow = "auto";
            fieldContainer.style.display = "block"; // Ensure it's displayed as a block

            // Create a label for the field
            const fieldLabel = document.createElement("label");
            fieldLabel.textContent = field.label;
            fieldLabel.style.fontWeight = "bold";
            fieldLabel.style.marginBottom = "5px";
            fieldLabel.style.display = "block";

            // Add the label to the container
            fieldContainer.appendChild(fieldLabel);

            // Create the input based on the field type
            if (field.type === "text") {
              // Create a text input
              const input = document.createElement("input");
              input.type = "text";
              input.placeholder = field.placeholder;
              input.value =
                deviceItem.device_counter.template[field.key]?.value || "";
              input.style.width = "100%";
              input.style.padding = "6px";
              input.style.borderRadius = "4px";
              input.style.border = "1px solid #ddd";

              // Add event listener to input
              input.addEventListener("input", (e) => {
                // Initialize the field if it doesn't exist
                if (!deviceItem.device_counter.template[field.key]) {
                  deviceItem.device_counter.template[field.key] = {
                    field_id: field.key === "device" ? "14841" : "14843",
                    label: field.label,
                    input_type: "string",
                    value: "",
                  };
                }

                // Update the field value
                deviceItem.device_counter.template[field.key].value =
                  e.target.value;
                deviceItem.device_counter.template[field.key].modified_by =
                  "ABSTRACTOR";

                // Update the modified_by display
                deviceModifiedByDisplay.textContent = "ABSTRACTOR";

                // Update the highlight using the existing updateTileStyle function
                let hasValue = e.target.value ? true : false;

                // Check if any other field has a value if this field is empty
                if (!hasValue) {
                  Object.values(deviceItem.device_counter.template).forEach(
                    (otherField) => {
                      if (otherField !== field && otherField.value) {
                        hasValue = true;
                      }
                    }
                  );
                }

                // Update the tile style
                updateTileStyle(
                  deviceItemContainer,
                  hasValue ? "filled" : "",
                  "modified"
                );
              });

              // Add the input to the container
              fieldContainer.appendChild(input);
            } else if (field.type === "select") {
              // Create a select dropdown
              const select = document.createElement("select");
              select.style.width = "100%";
              select.style.padding = "6px";
              select.style.borderRadius = "4px";
              select.style.border = "1px solid #ddd";

              // Add a default option if no value is set
              const currentValue = deviceItem.device_counter.template[field.key]?.value;
              if (!currentValue) {
                const defaultOption = document.createElement("option");
                defaultOption.value = "";
                defaultOption.innerText = "Select Device";
                defaultOption.disabled = true;
                defaultOption.selected = true;
                select.appendChild(defaultOption);
              }

              // Add options to the select dropdown
              field.options.forEach((option) => {
                if (typeof option === 'object') {
                  const optionElement = document.createElement("option");
                  optionElement.value = option.id;
                  optionElement.innerText = option.value;
                  if (currentValue && currentValue === option.value) {
                    optionElement.selected = true;
                  }
                  select.appendChild(optionElement);
                }
              });

              // Add event listener for select change
              select.addEventListener("change", (e) => {
                const selectedOption = field.options.find(
                  (option) => typeof option === 'object' && option.id === e.target.value
                );
                const newValue = selectedOption && typeof selectedOption === 'object' ? selectedOption.value : "";

                // Initialize the field if it doesn't exist
                if (!deviceItem.device_counter.template[field.key]) {
                  deviceItem.device_counter.template[field.key] = {
                    field_id: "14841",
                    label: field.label,
                    input_type: "select",
                    value: "",
                    options: field.options
                  };
                }

                // Update the field value
                deviceItem.device_counter.template[field.key].value = newValue;
                deviceItem.device_counter.template[field.key].modified_by = "ABSTRACTOR";

                // Update the modified_by display
                deviceModifiedByDisplay.textContent = "ABSTRACTOR";

                // Update the highlight using the existing updateTileStyle function
                let hasValue = newValue ? true : false;

                // Check if any other field has a value if this field is empty
                if (!hasValue) {
                  Object.values(deviceItem.device_counter.template).forEach(
                    (otherField) => {
                      if (otherField !== field && otherField.value) {
                        hasValue = true;
                      }
                    }
                  );
                }

                // Update the tile style
                updateTileStyle(
                  deviceItemContainer,
                  hasValue ? "filled" : "",
                  "modified"
                );
              });

              // Add the select to the container
              fieldContainer.appendChild(select);
            } else if (field.type === "radio") {
              // Create a container for the radio options
              const radioContainer = document.createElement("div");
              radioContainer.style.display = "flex";
              radioContainer.style.flexDirection = "column";
              radioContainer.style.gap = "5px";

              // Create a radio button for each option
              field.options.forEach((option) => {
                // Create a container for the radio option
                const radioOptionContainer = document.createElement("div");
                radioOptionContainer.style.display = "flex";
                radioOptionContainer.style.alignItems = "center";
                radioOptionContainer.style.gap = "5px";

                // Create the radio button
                const radio = document.createElement("input");
                radio.type = "radio";
                radio.name = `${field.key}-${deviceIndex}`;
                radio.value = option;
                radio.checked =
                  deviceItem.device_counter.template[field.key]?.value ===
                  option;

                // Create a label for the radio button
                const radioLabel = document.createElement("label");
                radioLabel.textContent = option;

                // Add event listener to radio button
                radio.addEventListener("change", () => {
                  // Initialize the field if it doesn't exist
                  if (!deviceItem.device_counter.template[field.key]) {
                    deviceItem.device_counter.template[field.key] = {
                      field_id:
                        field.key === "laa_isolation_approach"
                          ? "14844"
                          : "14968",
                      label: field.label,
                      input_type: field.conditional
                        ? "multi_input_field"
                        : "radio",
                      options: field.options,
                      value: "",
                    };

                    // Add conditional properties if needed
                    if (field.conditional) {
                      deviceItem.device_counter.template[field.key].if_yes = {
                        [field.conditionalKey]: {
                          field_id: "14845",
                          label: field.conditionalLabel,
                          options: field.conditionalOptions,
                          input_type: field.conditionalType,
                          value: "",
                        },
                      };
                    }
                  }

                  // Update the field value
                  deviceItem.device_counter.template[field.key].value = option;
                  deviceItem.device_counter.template[field.key].modified_by =
                    "ABSTRACTOR";

                  // Re-render the form to show/hide conditional fields
                  renderDevices();
                });

                // Add the radio button and label to the container
                radioOptionContainer.appendChild(radio);
                radioOptionContainer.appendChild(radioLabel);

                // Add the radio option container to the radio container
                radioContainer.appendChild(radioOptionContainer);
              });

              // Add the radio container to the field container
              fieldContainer.appendChild(radioContainer);

              // Render conditional field if needed
              if (
                field.conditional &&
                deviceItem.device_counter.template[field.key]?.value === "Yes"
              ) {
                // Create a container for the conditional field
                const conditionalContainer = document.createElement("div");
                conditionalContainer.style.marginTop = "10px";
                conditionalContainer.style.marginLeft = "20px";
                conditionalContainer.style.padding = "10px";
                conditionalContainer.style.borderLeft = "2px solid #ddd";

                // Create a label for the conditional field
                const conditionalLabel = document.createElement("label");
                conditionalLabel.textContent = field.conditionalLabel;
                conditionalLabel.style.fontWeight = "bold";
                conditionalLabel.style.marginBottom = "5px";
                conditionalLabel.style.display = "block";

                // Add the label to the container
                conditionalContainer.appendChild(conditionalLabel);

                // Create a select for the conditional field
                const select = document.createElement("select");
                select.style.width = "100%";
                select.style.padding = "6px";
                select.style.borderRadius = "4px";
                select.style.border = "1px solid #ddd";

                // Add a default option
                const defaultOption = document.createElement("option");
                defaultOption.value = "";
                defaultOption.textContent = "Select an option";
                defaultOption.disabled = true;
                defaultOption.selected =
                  !deviceItem.device_counter.template[field.key].if_yes[
                    field.conditionalKey
                  ].value;
                select.appendChild(defaultOption);

                // Add an option for each conditional option
                field.conditionalOptions.forEach((option) => {
                  const optionElement = document.createElement("option");
                  optionElement.value = option.id;
                  optionElement.textContent = option.value;
                  optionElement.selected =
                    deviceItem.device_counter.template[field.key].if_yes[
                      field.conditionalKey
                    ].value === option.value;
                  select.appendChild(optionElement);
                });

                // Add event listener to select
                select.addEventListener("change", (e) => {
                  // Find the selected option
                  const selectedOption = field.conditionalOptions.find(
                    (option) => option.id === e.target.value
                  );

                  // Update the field value
                  deviceItem.device_counter.template[field.key].if_yes[
                    field.conditionalKey
                  ].value = selectedOption.value;
                  deviceItem.device_counter.template[field.key].if_yes[
                    field.conditionalKey
                  ].modified_by = "ABSTRACTOR";
                });

                // Add the select to the container
                conditionalContainer.appendChild(select);

                // Add the conditional container to the field container
                fieldContainer.appendChild(conditionalContainer);
              }
            }

            // Add the field container to the form
            deviceForm.appendChild(fieldContainer);
          });

          // Add the form to the device item container
          deviceItemContainer.appendChild(deviceForm);

          // Add the device item container and modified_by display to the wrapper
          deviceItemWrapper.appendChild(deviceItemContainer);
          deviceItemWrapper.appendChild(deviceModifiedByDisplay);

          // Add the device item wrapper to the device items container
          deviceItemsContainer.appendChild(deviceItemWrapper);
        }
      );
    }

    // Render the devices
    renderDevices();

    // Add the device items container to the devices container
    devicesContainer.appendChild(deviceItemsContainer);

    // Add the devices container to the form
    accessSystemForm.appendChild(devicesContainer);

    // Add the form to the access system item container
    accessSystemItemContainer.appendChild(accessSystemForm);

    // Add the access system item container and modified_by display to the wrapper
    accessSystemItemWrapper.appendChild(accessSystemItemContainer);
    accessSystemItemWrapper.appendChild(modifiedByDisplay);

    // Add the access system item wrapper to the access system items container
    accessSystemItemsContainer.appendChild(accessSystemItemWrapper);
  }

  // Add event listener to add access system button
  addAccessSystemButton.addEventListener("click", () => {
    // Add a new access system item
    procedureInfo.device_information.access_systems.items.push({});

    // Render the new access system item
    renderAccessSystemItem(
      procedureInfo.device_information.access_systems.items[
        procedureInfo.device_information.access_systems.items.length - 1
      ],
      procedureInfo.device_information.access_systems.items.length - 1
    );
  });

  // Render existing access system items
  if (
    procedureInfo.device_information.access_systems.items &&
    procedureInfo.device_information.access_systems.items.length > 0
  ) {
    procedureInfo.device_information.access_systems.items.forEach(
      (item, idx) => {
        renderAccessSystemItem(item, idx);
      }
    );
  }

  // Add the access system items container to the access systems container
  accessSystemsContainer.appendChild(accessSystemItemsContainer);

  // Add the access systems container to the category container
  categoryContainer.appendChild(accessSystemsContainer);
}

// This function is used internally by renderProcedureInfo
function renderCategoryFields(
  patientData,
  container,
  category,
  fields,
  skipFields,
  categoryContainer
) {
  // Iterate through each field in the category
  Object.entries(fields).forEach(([key, value]) => {
    // Skip fields that are in the skip list
    if (skipFields.has(`${category}-${key}`)) {
      return;
    }

    // Skip the verified field as it's handled separately
    if (key === "verified") {
      return;
    }

    // Create a wrapper for this field tile
    const tileWrapper = document.createElement("div");
    tileWrapper.classList.add("tile-wrapper");
    tileWrapper.style.position = "relative";
    tileWrapper.style.marginBottom = "24px";

    // Create the field container
    const fieldContainer = document.createElement("div");
    fieldContainer.classList.add("field-container");

    // Create and append the label
    const label = document.createElement("label");
    label.classList.add("label", "cursor-pointer");
    label.textContent = `${value.label} ${
      value.field_id ? `(${value.field_id})` : ""
    }`;
    if (value.description) {
      label.setAttribute("title", value.description);
    }
    fieldContainer.appendChild(label);

    // Create the modified_by display
    const modifiedByDisplay = document.createElement("span");
    modifiedByDisplay.style.display = "block";
    modifiedByDisplay.style.textAlign = "right";
    modifiedByDisplay.style.marginTop = "-10px";
    modifiedByDisplay.style.color = "#8143d9";
    modifiedByDisplay.style.fontSize = "12px";
    if (!value.modified_by) {
      value.modified_by = "";
    }
    modifiedByDisplay.textContent = value.modified_by;

    // Process the field based on its input type
    if (value.input_type === "string" || value.input_type === "text") {
      // Handle string/text input
      const input = document.createElement("input");
      input.type = "text";
      input.name = key;
      input.value = value.value || "";
      input.placeholder = "Enter value...";

      // Track previous value for validation
      let previousValue = input.value;

      // Add event listener for input changes
      input.addEventListener("input", (e) => {
        const currentValue = e.target.value;

        // Use our existing validation utilities with a temporary callback
        // that tells us if the value was accepted
        let isValid = false;
        validateStringInput(currentValue, value.field_id, (validatedValue) => {
          isValid = true;
          previousValue = validatedValue;

          // Update the model and UI only if the value is valid
          patientData.procedureInfo[category][key].value = validatedValue;
          patientData.procedureInfo[category][key].modified_by = "ABSTRACTOR";
          updateTileStyle(fieldContainer, validatedValue);
          modifiedByDisplay.textContent = "ABSTRACTOR";
          updateContainerHighlight(container, patientData.procedureInfo);

          // Update the input value if the validation modified it
          if (validatedValue !== currentValue) {
            e.target.value = validatedValue;
          }
        });

        // If validation failed, revert to the previous value
        if (!isValid) {
          e.target.value = previousValue;
        }
      });

      // Add the input to the field container
      fieldContainer.appendChild(input);
      updateTileStyle(fieldContainer, value.value);
    } else if (value.input_type === "date") {
      // Handle date input
      const dateWrapper = document.createElement("div");
      dateWrapper.style.position = "relative";

      // Create display input
      const displayInput = document.createElement("input");
      displayInput.type = "text";
      displayInput.name = `${key}_display`;
      displayInput.readOnly = true;
      displayInput.value = formatDisplayDate(value.value);
      displayInput.placeholder = "MM/DD/YYYY";
      displayInput.style.cursor = "pointer";

      // Hidden date input
      const dateInput = document.createElement("input");
      dateInput.type = "date";
      dateInput.name = key;
      dateInput.value = value.value || "";
      dateInput.style.position = "absolute";
      dateInput.style.opacity = "0";
      dateInput.style.cursor = "pointer";

      // Set max date to today
      const today = new Date().toISOString().split("T")[0];
      dateInput.max = today;

      updateTileStyle(fieldContainer, dateInput.value);

      dateInput.addEventListener("change", (e) => {
        const selectedDate = e.target.value;
        displayInput.value = formatDisplayDate(selectedDate);
        // Ensure the field has the expected structure
        if (
          typeof patientData.procedureInfo[category][key] !== "object" ||
          patientData.procedureInfo[category][key] === null
        ) {
          patientData.procedureInfo[category][key] = {
            value: selectedDate,
            modified_by: "ABSTRACTOR",
          };
        } else {
          patientData.procedureInfo[category][key].value = selectedDate;
          patientData.procedureInfo[category][key].modified_by = "ABSTRACTOR";
        }
        updateTileStyle(fieldContainer, selectedDate);
        modifiedByDisplay.textContent = "ABSTRACTOR";
        updateContainerHighlight(container, patientData.procedureInfo);
      });

      // Trigger date picker when clicking display input
      displayInput.addEventListener("click", () => {
        dateInput.showPicker();
      });

      dateWrapper.appendChild(displayInput);
      dateWrapper.appendChild(dateInput);
      fieldContainer.appendChild(dateWrapper);
    } else if (value.input_type === "date_time") {
      // Handle date_time input
      const dateTimeWrapper = document.createElement("div");
      dateTimeWrapper.style.position = "relative";
      dateTimeWrapper.style.display = "flex";
      dateTimeWrapper.style.flexDirection = "column";
      dateTimeWrapper.style.gap = "8px";

      // Create display input for the combined date and time
      const displayInput = document.createElement("input");
      displayInput.type = "text";
      displayInput.name = `${key}_display`;
      displayInput.readOnly = true;
      displayInput.value = formatDisplayDateTime(value.value);
      displayInput.placeholder = "MM/DD/YYYY HH:MM AM/PM";
      displayInput.style.cursor = "pointer";

      // Create a container for the date and time pickers
      const pickersContainer = document.createElement("div");
      pickersContainer.style.display = "flex";
      pickersContainer.style.gap = "8px";
      pickersContainer.style.position = "absolute";
      pickersContainer.style.opacity = "0";
      pickersContainer.style.pointerEvents = "none";

      // Hidden date input
      const dateInput = document.createElement("input");
      dateInput.type = "date";
      dateInput.name = `${key}_date`;
      dateInput.style.cursor = "pointer";

      // Hidden time input
      const timeInput = document.createElement("input");
      timeInput.type = "time";
      timeInput.name = `${key}_time`;
      timeInput.style.cursor = "pointer";

      // Set initial values if available
      if (value.value) {
        const dateObj = new Date(value.value);
        if (!isNaN(dateObj.getTime())) {
          // Set date value (YYYY-MM-DD)
          dateInput.value = dateObj.toISOString().split("T")[0];

          // Set time value (HH:MM)
          const hours = String(dateObj.getHours()).padStart(2, "0");
          const minutes = String(dateObj.getMinutes()).padStart(2, "0");
          timeInput.value = `${hours}:${minutes}`;
        }
      }

      // Set max date to today
      const today = new Date().toISOString().split("T")[0];
      dateInput.max = today;

      updateTileStyle(fieldContainer, value.value);

      // Function to update the combined date-time value
      const updateDateTime = () => {
        if (dateInput.value) {
          let dateTimeValue;

          if (timeInput.value) {
            // Combine date and time
            const [hours, minutes] = timeInput.value.split(":");
            const dateObj = new Date(dateInput.value);
            dateObj.setHours(parseInt(hours, 10));
            dateObj.setMinutes(parseInt(minutes, 10));
            dateTimeValue = dateObj.toISOString();
          } else {
            // Use just the date with time set to midnight
            dateTimeValue = `${dateInput.value}T00:00:00.000Z`;
          }

          // Update display and model
          displayInput.value = formatDisplayDateTime(dateTimeValue);
          // Ensure the field has the expected structure
          if (
            typeof patientData.procedureInfo[category][key] !== "object" ||
            patientData.procedureInfo[category][key] === null
          ) {
            patientData.procedureInfo[category][key] = {
              value: dateTimeValue,
              modified_by: "ABSTRACTOR",
            };
          } else {
            patientData.procedureInfo[category][key].value = dateTimeValue;
            patientData.procedureInfo[category][key].modified_by = "ABSTRACTOR";
          }
          updateTileStyle(fieldContainer, dateTimeValue);
          modifiedByDisplay.textContent = "ABSTRACTOR";
          updateContainerHighlight(container, patientData.procedureInfo);
        }
      };

      // Event listeners for date and time inputs
      dateInput.addEventListener("change", updateDateTime);
      timeInput.addEventListener("change", updateDateTime);

      // Show date picker when clicking the display input
      displayInput.addEventListener("click", () => {
        // Make the pickers container visible temporarily
        pickersContainer.style.opacity = "1";
        pickersContainer.style.pointerEvents = "auto";
        pickersContainer.style.zIndex = "100";

        // Show the date picker first
        dateInput.showPicker();

        // After date is selected, show time picker
        dateInput.addEventListener(
          "change",
          function onDateSelected() {
            setTimeout(() => {
              timeInput.showPicker();
            }, 100);
            dateInput.removeEventListener("change", onDateSelected);
          },
          { once: true }
        );

        // Hide the pickers container after time is selected
        timeInput.addEventListener(
          "change",
          function onTimeSelected() {
            setTimeout(() => {
              pickersContainer.style.opacity = "0";
              pickersContainer.style.pointerEvents = "none";
            }, 500);
            timeInput.removeEventListener("change", onTimeSelected);
          },
          { once: true }
        );
      });

      // Add inputs to their containers
      pickersContainer.appendChild(dateInput);
      pickersContainer.appendChild(timeInput);
      dateTimeWrapper.appendChild(displayInput);
      dateTimeWrapper.appendChild(pickersContainer);
      fieldContainer.appendChild(dateTimeWrapper);
    } else if (value.input_type === "radio") {
      // Handle radio input
      const radioContainer = document.createElement("div");
      radioContainer.classList.add("radio-container");
      radioContainer.style.display = "flex";
      radioContainer.style.flexDirection = "column"; // Changed to column for vertical stacking
      radioContainer.style.gap = "10px";

      value.options.forEach((option) => {
        const radioWrapper = document.createElement("div");
        radioWrapper.style.display = "flex";
        radioWrapper.style.alignItems = "center";
        radioWrapper.style.marginBottom = "10px"; // Added margin for better spacing

        const input = document.createElement("input");
        input.type = "radio";
        input.name = key;
        input.value = option;
        input.id = `${key}-${option}`;
        input.checked = value.value === option;

        input.addEventListener("change", (e) => {
          if (e.target.checked) {
            // Ensure the field has the expected structure
            if (
              typeof patientData.procedureInfo[category][key] !== "object" ||
              patientData.procedureInfo[category][key] === null
            ) {
              patientData.procedureInfo[category][key] = {
                value: e.target.value,
                modified_by: "ABSTRACTOR",
              };
            } else {
              patientData.procedureInfo[category][key].value = e.target.value;
              patientData.procedureInfo[category][key].modified_by =
                "ABSTRACTOR";
            }
            updateTileStyle(fieldContainer, e.target.value);
            modifiedByDisplay.textContent = "ABSTRACTOR";
            updateContainerHighlight(container, patientData.procedureInfo);
          }
        });

        const optionLabel = document.createElement("label");
        optionLabel.setAttribute("for", `${key}-${option}`);
        optionLabel.innerText = option;
        if (value.description) {
          optionLabel.setAttribute("title", value.description);
        }

        radioWrapper.appendChild(input);
        radioWrapper.appendChild(optionLabel);
        radioContainer.appendChild(radioWrapper);
      });

      fieldContainer.appendChild(radioContainer);
      updateTileStyle(fieldContainer, value.value || "");
    } else if (value.input_type === "select") {
      // Handle select input
      const select = document.createElement("select");
      select.name = key;

      // If no value is set, add a default option
      if (!value.value) {
        const defaultOption = document.createElement("option");
        defaultOption.value = "";
        defaultOption.innerText = "Select an option";
        defaultOption.disabled = true;
        defaultOption.selected = true;
        select.appendChild(defaultOption);
      }

      value.options.forEach((option) => {
        const optionElement = document.createElement("option");
        optionElement.value = option.id;
        optionElement.innerText = option.value;
        if (value.value && value.value === option.value) {
          optionElement.selected = true;
        }
        select.appendChild(optionElement);
      });

      select.addEventListener("change", (e) => {
        const selectedOption = value.options.find(
          (option) => option.id === e.target.value
        );
        const newValue = selectedOption ? selectedOption.value : "";

        // Ensure the field has the expected structure
        if (
          typeof patientData.procedureInfo[category][key] !== "object" ||
          patientData.procedureInfo[category][key] === null
        ) {
          patientData.procedureInfo[category][key] = {
            value: newValue,
            modified_by: "ABSTRACTOR",
          };
        } else {
          patientData.procedureInfo[category][key].value = newValue;
          patientData.procedureInfo[category][key].modified_by = "ABSTRACTOR";
        }

        updateTileStyle(fieldContainer, newValue);
        modifiedByDisplay.textContent = "ABSTRACTOR";
        updateContainerHighlight(container, patientData.procedureInfo);
      });

      fieldContainer.appendChild(select);
      updateTileStyle(fieldContainer, value.value || "");
    } else if (value.input_type === "multi_select") {
      // Handle multi_select input
      // Initialize the value as an array if it's not already
      if (!Array.isArray(value.value)) {
        value.value = value.value ? [value.value] : [];
      }

      // Create dropdown container with relative positioning
      const dropdownContainer = document.createElement("div");
      dropdownContainer.classList.add("dropdown-container");
      dropdownContainer.style.position = "relative";
      dropdownContainer.style.width = "100%";

      // Make sure all parent containers allow overflow
      fieldContainer.style.overflow = "visible";
      if (fieldContainer.parentElement) {
        fieldContainer.parentElement.style.overflow = "visible";
      }
      container.style.overflow = "visible";

      // Create dropdown header/button
      const dropdownHeader = document.createElement("div");
      dropdownHeader.classList.add("dropdown-header");
      dropdownHeader.style.padding = "8px 12px";
      dropdownHeader.style.border = "1px solid #ccc";
      dropdownHeader.style.borderRadius = "4px";
      dropdownHeader.style.cursor = "pointer";
      dropdownHeader.style.display = "flex";
      dropdownHeader.style.justifyContent = "space-between";
      dropdownHeader.style.alignItems = "center";
      dropdownHeader.style.backgroundColor = "#fff";

      // Display selected values or placeholder
      const selectedText = document.createElement("span");
      selectedText.classList.add("selected-text");

      // Function to update the selected text display
      const updateSelectedText = () => {
        if (value.value.length === 0) {
          selectedText.textContent = "Select options...";
        } else if (value.value.length === 1) {
          const selectedOption = value.options.find((opt) => {
            const optValue = typeof opt === "object" ? opt.value : opt;
            return optValue === value.value[0];
          });
          selectedText.textContent = selectedOption
            ? typeof selectedOption === "object"
              ? selectedOption.value
              : selectedOption
            : value.value[0];
        } else {
          selectedText.textContent = `${value.value.length} options selected`;
        }
      };

      updateSelectedText();

      // Add dropdown arrow
      const dropdownArrow = document.createElement("span");
      dropdownArrow.innerHTML = "&#9662;"; // Down arrow character

      dropdownHeader.appendChild(selectedText);
      dropdownHeader.appendChild(dropdownArrow);

      // Create dropdown content (initially hidden)
      const dropdownContent = document.createElement("div");
      dropdownContent.classList.add("dropdown-content");
      dropdownContent.style.display = "none";
      dropdownContent.style.position = "absolute";
      dropdownContent.style.top = "100%";
      dropdownContent.style.left = "0";
      dropdownContent.style.width = "100%";
      dropdownContent.style.maxHeight = "200px";
      dropdownContent.style.overflowY = "auto";
      dropdownContent.style.backgroundColor = "#fff";
      dropdownContent.style.border = "1px solid #ccc";
      dropdownContent.style.borderRadius = "4px";
      dropdownContent.style.boxShadow = "0 4px 8px rgba(0,0,0,0.1)";
      dropdownContent.style.zIndex = "1000";

      // Create a checkbox for each option
      value.options.forEach((option) => {
        // Handle both object options and string options
        const optionValue = typeof option === "object" ? option.value : option;
        const optionId = typeof option === "object" ? option.id : option;

        const checkboxWrapper = document.createElement("div");
        checkboxWrapper.classList.add("checkbox-wrapper");
        checkboxWrapper.style.display = "flex";
        checkboxWrapper.style.alignItems = "center";
        checkboxWrapper.style.padding = "8px 12px";
        checkboxWrapper.style.borderBottom = "1px solid #eee";

        const input = document.createElement("input");
        input.type = "checkbox";
        input.name = `${key}-${optionId}`;
        input.value = optionId;
        input.id = `${key}-${optionId}`;
        input.style.marginRight = "8px";

        // Check if this option is in the selected values array
        input.checked = value.value.includes(optionValue);

        input.addEventListener("change", (e) => {
          // Get the current values array
          let currentValues = Array.isArray(value.value)
            ? [...value.value]
            : [];

          if (e.target.checked) {
            // Add the value if it's not already in the array
            if (!currentValues.includes(optionValue)) {
              currentValues.push(optionValue);
            }
          } else {
            // Remove the value if it's in the array
            currentValues = currentValues.filter((val) => val !== optionValue);
          }

          // Update the model
          // Ensure the field has the expected structure
          if (
            typeof patientData.procedureInfo[category][key] !== "object" ||
            patientData.procedureInfo[category][key] === null
          ) {
            patientData.procedureInfo[category][key] = {
              value: currentValues,
              modified_by: "ABSTRACTOR",
            };
          } else {
            patientData.procedureInfo[category][key].value = currentValues;
            patientData.procedureInfo[category][key].modified_by = "ABSTRACTOR";
          }

          // Update selected text display
          updateSelectedText();
          updateTileStyle(
            fieldContainer,
            currentValues.length > 0 ? "filled" : ""
          );
          modifiedByDisplay.textContent = "ABSTRACTOR";
          updateContainerHighlight(container, patientData.procedureInfo);
        });

        const optionLabel = document.createElement("label");
        optionLabel.setAttribute("for", `${key}-${optionId}`);
        if (typeof option === "object" && option.field_id) {
          optionLabel.innerText = `${optionValue} (${option.field_id})`;
        } else {
          optionLabel.innerText = optionValue;
        }

        checkboxWrapper.appendChild(input);
        checkboxWrapper.appendChild(optionLabel);
        dropdownContent.appendChild(checkboxWrapper);
      });

      // Toggle dropdown on click
      dropdownHeader.addEventListener("click", () => {
        const isOpen = dropdownContent.style.display === "block";
        dropdownContent.style.display = isOpen ? "none" : "block";
        dropdownArrow.innerHTML = isOpen ? "&#9662;" : "&#9652;";
      });

      // Close dropdown when clicking outside
      document.addEventListener("click", (e) => {
        if (!dropdownContainer.contains(e.target)) {
          dropdownContent.style.display = "none";
          dropdownArrow.innerHTML = "&#9662;";
        }
      });

      dropdownContainer.appendChild(dropdownHeader);
      dropdownContainer.appendChild(dropdownContent);
      fieldContainer.appendChild(dropdownContainer);
      updateTileStyle(fieldContainer, value.value.length > 0 ? "filled" : "");
    } else if (value.input_type === "multi_input_field") {
      // Handle multi_input_field
      const multiInputContainer = document.createElement("div");
      multiInputContainer.classList.add("multi-input-container");
      multiInputContainer.style.marginTop = "10px";

      // Create radio buttons container
      const radioContainer = document.createElement("div");
      radioContainer.classList.add("radio-container");
      radioContainer.style.display = "flex";
      radioContainer.style.flexDirection = "column"; // Changed to column for vertical stacking
      radioContainer.style.gap = "10px";
      radioContainer.style.width = "100%";

      // Create container for conditional content
      const conditionalContainer = document.createElement("div");
      conditionalContainer.classList.add("conditional-content-container");
      conditionalContainer.style.marginTop = "10px";
      conditionalContainer.style.padding = "10px";
      conditionalContainer.style.border = "1px solid #eee";
      conditionalContainer.style.borderRadius = "4px";
      conditionalContainer.style.display = "none"; // Initially hidden

      // Create radio buttons for each option
      value.options.forEach((option) => {
        const radioWrapper = document.createElement("div");
        radioWrapper.style.display = "flex";
        radioWrapper.style.alignItems = "center";
        radioWrapper.style.marginBottom = "10px"; // Added margin for better spacing

        const input = document.createElement("input");
        input.type = "radio";
        input.name = key;
        input.value = option;
        input.id = `${key}-${option}`;
        input.checked = value.value === option;

        input.addEventListener("change", (e) => {
          if (e.target.checked) {
            // Update the field value
            // Ensure the field has the expected structure
            if (
              typeof patientData.procedureInfo[category][key] !== "object" ||
              patientData.procedureInfo[category][key] === null
            ) {
              patientData.procedureInfo[category][key] = {
                value: e.target.value,
                modified_by: "ABSTRACTOR",
              };
            } else {
              patientData.procedureInfo[category][key].value = e.target.value;
              patientData.procedureInfo[category][key].modified_by =
                "ABSTRACTOR";
            }
            updateTileStyle(fieldContainer, e.target.value);
            modifiedByDisplay.textContent = "ABSTRACTOR";
            updateContainerHighlight(container, patientData.procedureInfo);

            // Clear values from other conditional sections when switching options
            if (e.target.value === "No" && value.if_yes) {
              clearConditionalValues(value, "if_yes");
            } else if (e.target.value.startsWith("Yes") && value.if_no) {
              clearConditionalValues(value, "if_no");
            } else if (e.target.value === "Deceased" && value.if_alive) {
              clearConditionalValues(value, "if_alive");
            } else if (e.target.value === "Alive" && value.if_deceased) {
              clearConditionalValues(value, "if_deceased");
            }

            // Clear and render conditional content
            conditionalContainer.innerHTML = "";
            conditionalContainer.style.display = "none";

            // Determine which conditional content to show
            let conditionalKey = null;
            if (e.target.value.startsWith("Yes") && value.if_yes) {
              conditionalKey = "if_yes";
            } else if (e.target.value === "No" && value.if_no) {
              conditionalKey = "if_no";
            } else if (e.target.value === "Alive" && value.if_alive) {
              conditionalKey = "if_alive";
            } else if (e.target.value === "Deceased" && value.if_deceased) {
              conditionalKey = "if_deceased";
            }

            // Render conditional content if available
            if (conditionalKey && value[conditionalKey]) {
              renderConditionalContent(
                e.target.value,
                value,
                conditionalContainer,
                key
              );
            }
          }
        });

        const optionLabel = document.createElement("label");
        optionLabel.setAttribute("for", `${key}-${option}`);
        optionLabel.innerText = option;
        if (value.description) {
          optionLabel.setAttribute("title", value.description);
        }

        radioWrapper.appendChild(input);
        radioWrapper.appendChild(optionLabel);
        radioContainer.appendChild(radioWrapper);
      });

      multiInputContainer.appendChild(radioContainer);
      multiInputContainer.appendChild(conditionalContainer);
      fieldContainer.appendChild(multiInputContainer);

      // If a value is already selected, render the conditional content
      if (value.value) {
        // Determine which conditional content to show
        let conditionalKey = null;
        if (value.value.startsWith("Yes") && value.if_yes) {
          conditionalKey = "if_yes";
        } else if (value.value === "No" && value.if_no) {
          conditionalKey = "if_no";
        } else if (value.value === "Alive" && value.if_alive) {
          conditionalKey = "if_alive";
        } else if (value.value === "Deceased" && value.if_deceased) {
          conditionalKey = "if_deceased";
        }

        // Render conditional content if available
        if (conditionalKey && value[conditionalKey]) {
          renderConditionalContent(
            value.value,
            value,
            conditionalContainer,
            key
          );
        }
      }

      updateTileStyle(fieldContainer, value.value || "");
    }

    // Append the field container and modified_by display to the tile wrapper
    tileWrapper.appendChild(fieldContainer);
    tileWrapper.appendChild(modifiedByDisplay);
    categoryContainer.appendChild(tileWrapper);
  });
}

export function renderProcedureInfo(patientData) {
  const container = document.getElementById("procedureInfo");
  container.innerHTML = ""; // Clear the container before rendering
  container.style.position = "relative"; // To ensure the verified checkbox is positioned within the section

  // Create a Map to track fields that should be skipped (with category and key)
  const skipFields = new Map();

  // First pass: identify fields with dependencies and their dependent fields
  Object.entries(patientData.procedureInfo).forEach(([category, fields]) => {
    if (category === "verified" || !fields || Object.keys(fields).length === 0)
      return;

    // Handle nested structure for pre_procedure_diagnostics
    if (category === "pre_procedure_diagnostics" && fields.elements) {
      // Process the elements inside pre_procedure_diagnostics
      Object.entries(fields.elements).forEach(([fieldKey, value]) => {
        if (value.dependency) {
          // This field has a dependency, find the field it depends on
          const dependentFieldKey = Object.keys(fields.elements).find(
            (k) => fields.elements[k].field_id === value.dependency
          );

          if (dependentFieldKey) {
            // Add the dependent field to the skip list (category + key)
            skipFields.set(`${category}-${dependentFieldKey}`, true);
            // Also add the field with dependency to the skip list
            skipFields.set(`${category}-${fieldKey}`, true);
          }
        }
      });
    }
    // Handle device_information category with access_systems repeatable container
    else if (
      category === "device_information" &&
      fields.access_systems &&
      fields.access_systems.input_type === "repeatable_container"
    ) {
      // Skip the access_systems field as we'll handle it specially
      skipFields.set(`${category}-access_systems`, true);
    } else {
      // Process normal fields
      Object.entries(fields).forEach(([fieldKey, value]) => {
        if (value.dependency) {
          // This field has a dependency, find the field it depends on
          const dependentFieldKey = Object.keys(fields).find(
            (k) => fields[k].field_id === value.dependency
          );

          if (dependentFieldKey) {
            // Add the dependent field to the skip list (category + key)
            skipFields.set(`${category}-${dependentFieldKey}`, true);
            // Also add the field with dependency to the skip list
            skipFields.set(`${category}-${fieldKey}`, true);
          }
        }
      });
    }
  });

  // Iterate over each category and its fields in the provided data.
  Object.entries(patientData.procedureInfo).forEach(([category, fields]) => {
    // Skip empty categories.
    if (!fields || Object.keys(fields).length === 0) {
      return;
    }
    // Skip container-level verified property.
    if (category === "verified") return;

    // Create a container for the category and flatten it in the grid.
    const categoryContainer = document.createElement("div");
    categoryContainer.classList.add("category-container");
    categoryContainer.style.display = "contents";

    // Create and style the category label.
    const categoryLabel = document.createElement("h4");
    categoryLabel.style.fontWeight = "bold";
    // Force the category label to span all grid columns.
    categoryLabel.style.gridColumn = "1 / -1";
    categoryLabel.innerText = formatHeading(category);
    categoryContainer.appendChild(categoryLabel);

    // Handle pre_procedure_diagnostics category differently
    if (category === "pre_procedure_diagnostics") {
      // Use the renderCategoryFields function for this category
      renderCategoryFields(
        patientData,
        container,
        category,
        fields,
        skipFields,
        categoryContainer
      );
    }
    // Handle device_information category with access_systems repeatable container
    else if (
      category === "device_information" &&
      fields.access_systems &&
      fields.access_systems.input_type === "repeatable_container"
    ) {
      // Render other fields in the device_information category
      renderCategoryFields(
        patientData,
        container,
        category,
        fields,
        skipFields,
        categoryContainer
      );

      // Render the access_systems repeatable container
      renderAccessSystems(
        fields.access_systems,
        categoryContainer,
        patientData.procedureInfo
      );
    } else {
      // Iterate over the fields in the current category.
      Object.entries(fields).forEach(([key, value]) => {
        // Skip fields that are dependencies or have dependencies
        if (skipFields.has(`${category}-${key}`)) return;
        // Create an outer wrapper (tile-wrapper) for border highlight and modified_by display.
        const tileWrapper = document.createElement("div");
        tileWrapper.classList.add("tile-wrapper");
        tileWrapper.style.position = "relative";
        tileWrapper.style.marginBottom = "24px";

        // Create the field container for the field.
        const fieldContainer = document.createElement("div");
        fieldContainer.classList.add("field-container");

        // Create a label for the field with proper classes.
        const label = document.createElement("label");
        label.classList.add("label", "cursor-pointer");
        label.innerHTML = `${value.label} (${value.field_id})`;
        // Add tooltip with description if provided.
        if (value.description) {
          label.setAttribute("title", value.description);
        }
        fieldContainer.appendChild(label);

        // If a metric is available, insert it after the label.
        if (value.metric) {
          const metricLabel = document.createElement("span");
          metricLabel.classList.add("metric");
          metricLabel.textContent = ` (${value.metric})`;
          fieldContainer.appendChild(metricLabel);
        }

        // Create the modified_by display element.
        const modifiedByDisplay = document.createElement("span");
        modifiedByDisplay.style.display = "block";
        modifiedByDisplay.style.textAlign = "right";
        modifiedByDisplay.style.marginTop = "-10px";
        modifiedByDisplay.style.color = "#8143d9";
        modifiedByDisplay.style.fontSize = "12px";
        // Initialize modified_by if not set.
        if (!value.modified_by) {
          value.modified_by = "";
        }
        modifiedByDisplay.textContent = value.modified_by;

        // Render input based on the input type.
        if (value.input_type === "string" || value.input_type === "text") {
          const input = document.createElement("input");
          input.type = "text";
          input.name = key;
          input.placeholder = `Enter ${value.label}`;
          input.value = value.value || "";

          updateTileStyle(fieldContainer, input.value);

          let previousValue = input.value;

          input.addEventListener("input", (e) => {
            const currentValue = e.target.value;

            // Use our existing validation utilities with a temporary callback
            // that tells us if the value was accepted
            let isValid = false;
            validateStringInput(
              currentValue,
              value.field_id,
              (validatedValue) => {
                isValid = true;
                previousValue = validatedValue;

                // Update the model and UI only if the value is valid
                // Ensure the field has the expected structure
                if (
                  typeof patientData.procedureInfo[category][key] !==
                    "object" ||
                  patientData.procedureInfo[category][key] === null
                ) {
                  patientData.procedureInfo[category][key] = {
                    value: validatedValue,
                    modified_by: "ABSTRACTOR",
                  };
                } else {
                  patientData.procedureInfo[category][key].value =
                    validatedValue;
                  patientData.procedureInfo[category][key].modified_by =
                    "ABSTRACTOR";
                }
                updateTileStyle(fieldContainer, validatedValue);
                modifiedByDisplay.textContent = "ABSTRACTOR";
                updateContainerHighlight(container, patientData.procedureInfo);

                // Update the input value if the validation modified it
                if (validatedValue !== currentValue) {
                  e.target.value = validatedValue;
                }
              }
            );

            // If the validation didn't accept the value, revert to previous valid value
            if (!isValid) {
              e.target.value = previousValue;
            }
          });

          fieldContainer.appendChild(input);
        } else if (value.input_type === "date") {
          const dateWrapper = document.createElement("div");
          dateWrapper.style.position = "relative";

          // Create display input
          const displayInput = document.createElement("input");
          displayInput.type = "text";
          displayInput.name = `${key}_display`;
          displayInput.readOnly = true;
          displayInput.value = formatDisplayDate(value.value);
          displayInput.placeholder = "MM/DD/YYYY"; // Added placeholder
          displayInput.style.cursor = "pointer";

          // Hidden date input
          const dateInput = document.createElement("input");
          dateInput.type = "date";
          dateInput.name = key;
          dateInput.value = value.value || "";
          dateInput.style.position = "absolute";
          dateInput.style.opacity = "0";
          dateInput.style.cursor = "pointer";

          // Set max date to today
          const today = new Date().toISOString().split("T")[0];
          dateInput.max = today;

          updateTileStyle(fieldContainer, dateInput.value);

          dateInput.addEventListener("change", (e) => {
            const selectedDate = e.target.value;
            displayInput.value = formatDisplayDate(selectedDate);
            patientData.procedureInfo[category][key].value = selectedDate;
            patientData.procedureInfo[category][key].modified_by = "ABSTRACTOR";
            updateTileStyle(fieldContainer, selectedDate);
            modifiedByDisplay.textContent = "ABSTRACTOR";
            updateContainerHighlight(container, patientData.procedureInfo);
          });

          // Trigger date picker when clicking display input
          displayInput.addEventListener("click", () => {
            dateInput.showPicker();
          });

          dateWrapper.appendChild(displayInput);
          dateWrapper.appendChild(dateInput);
          fieldContainer.appendChild(dateWrapper);
        } else if (value.input_type === "date_time") {
          const dateTimeWrapper = document.createElement("div");
          dateTimeWrapper.style.position = "relative";
          dateTimeWrapper.style.display = "flex";
          dateTimeWrapper.style.flexDirection = "column";
          dateTimeWrapper.style.gap = "8px";

          // Create display input for the combined date and time
          const displayInput = document.createElement("input");
          displayInput.type = "text";
          displayInput.name = `${key}_display`;
          displayInput.readOnly = true;
          displayInput.value = formatDisplayDateTime(value.value);
          displayInput.placeholder = "MM/DD/YYYY HH:MM AM/PM";
          displayInput.style.cursor = "pointer";

          // Create a container for the date and time pickers
          const pickersContainer = document.createElement("div");
          pickersContainer.style.display = "flex";
          pickersContainer.style.gap = "8px";
          pickersContainer.style.position = "absolute";
          pickersContainer.style.opacity = "0";
          pickersContainer.style.pointerEvents = "none";

          // Hidden date input
          const dateInput = document.createElement("input");
          dateInput.type = "date";
          dateInput.name = `${key}_date`;
          dateInput.style.cursor = "pointer";

          // Hidden time input
          const timeInput = document.createElement("input");
          timeInput.type = "time";
          timeInput.name = `${key}_time`;
          timeInput.style.cursor = "pointer";

          // Set initial values if available
          if (value.value) {
            const dateObj = new Date(value.value);
            if (!isNaN(dateObj.getTime())) {
              // Set date value (YYYY-MM-DD)
              dateInput.value = dateObj.toISOString().split("T")[0];

              // Set time value (HH:MM)
              const hours = String(dateObj.getHours()).padStart(2, "0");
              const minutes = String(dateObj.getMinutes()).padStart(2, "0");
              timeInput.value = `${hours}:${minutes}`;
            }
          }

          // Set max date to today
          const today = new Date().toISOString().split("T")[0];
          dateInput.max = today;

          updateTileStyle(fieldContainer, value.value);

          // Function to update the combined date-time value
          const updateDateTime = () => {
            if (dateInput.value) {
              let dateTimeValue;

              if (timeInput.value) {
                // Combine date and time
                const [hours, minutes] = timeInput.value.split(":");
                const dateObj = new Date(dateInput.value);
                dateObj.setHours(parseInt(hours, 10));
                dateObj.setMinutes(parseInt(minutes, 10));
                dateTimeValue = dateObj.toISOString();
              } else {
                // Use just the date with time set to midnight
                dateTimeValue = `${dateInput.value}T00:00:00.000Z`;
              }

              // Update display and model
              displayInput.value = formatDisplayDateTime(dateTimeValue);
              patientData.procedureInfo[category][key].value = dateTimeValue;
              patientData.procedureInfo[category][key].modified_by =
                "ABSTRACTOR";
              updateTileStyle(fieldContainer, dateTimeValue);
              modifiedByDisplay.textContent = "ABSTRACTOR";
              updateContainerHighlight(container, patientData.procedureInfo);
            }
          };

          // Event listeners for date and time inputs
          dateInput.addEventListener("change", updateDateTime);
          timeInput.addEventListener("change", updateDateTime);

          // Show date picker when clicking the display input
          displayInput.addEventListener("click", () => {
            // Make the pickers container visible temporarily
            pickersContainer.style.opacity = "1";
            pickersContainer.style.pointerEvents = "auto";
            pickersContainer.style.zIndex = "100";

            // Show the date picker first
            dateInput.showPicker();

            // After date is selected, show time picker
            dateInput.addEventListener(
              "change",
              function onDateSelected() {
                setTimeout(() => {
                  timeInput.showPicker();
                }, 100);
                dateInput.removeEventListener("change", onDateSelected);
              },
              { once: true }
            );

            // Hide the pickers container after time is selected
            timeInput.addEventListener(
              "change",
              function onTimeSelected() {
                setTimeout(() => {
                  pickersContainer.style.opacity = "0";
                  pickersContainer.style.pointerEvents = "none";
                }, 500);
                timeInput.removeEventListener("change", onTimeSelected);
              },
              { once: true }
            );
          });

          // Add inputs to their containers
          pickersContainer.appendChild(dateInput);
          pickersContainer.appendChild(timeInput);
          dateTimeWrapper.appendChild(displayInput);
          dateTimeWrapper.appendChild(pickersContainer);
          fieldContainer.appendChild(dateTimeWrapper);
        } else if (value.input_type === "radio") {
          // Create radio buttons container.
          const radioContainer = document.createElement("div");
          radioContainer.classList.add("radio-container");

          value.options.forEach((option) => {
            const radioWrapper = document.createElement("div");
            const input = document.createElement("input");
            input.type = "radio";
            input.name = key;
            input.value = option;
            input.id = `${key}-${option}`;
            input.checked = value.value === option;

            input.addEventListener("change", (event) => {
              patientData.procedureInfo[category][key].value =
                event.target.value;
              patientData.procedureInfo[category][key].modified_by =
                "ABSTRACTOR";
              updateTileStyle(fieldContainer, event.target.value);
              modifiedByDisplay.textContent = "ABSTRACTOR";
              updateContainerHighlight(container, patientData.procedureInfo);
            });

            const optionLabel = document.createElement("label");
            optionLabel.classList.add("label", "cursor-pointer");
            optionLabel.setAttribute("for", `${key}-${option}`);
            optionLabel.innerText = option;

            radioWrapper.appendChild(input);
            radioWrapper.appendChild(optionLabel);
            radioContainer.appendChild(radioWrapper);
          });
          fieldContainer.appendChild(radioContainer);
          updateTileStyle(fieldContainer, value.value || "");
        } else if (value.input_type === "select") {
          // Create a select dropdown.
          const select = document.createElement("select");
          select.name = key;

          // If no value is set, add a default option.
          if (!value.value) {
            const defaultOption = document.createElement("option");
            defaultOption.value = "";
            defaultOption.innerText = "Select an option";
            defaultOption.disabled = true;
            defaultOption.selected = true;
            select.appendChild(defaultOption);
          }

          value.options.forEach((option) => {
            const optionElement = document.createElement("option");
            // Assuming options have id and value fields.
            optionElement.value = option.id;
            optionElement.innerText = option.value;
            if (value.value && value.value === option.value) {
              optionElement.selected = true;
            }
            select.appendChild(optionElement);
          });

          // Update patientData on dropdown selection.
          select.addEventListener("change", (e) => {
            const selectedOption = value.options.find(
              (option) => option.id === e.target.value
            );
            patientData.procedureInfo[category][key].value =
              selectedOption.value;
            patientData.procedureInfo[category][key].modified_by = "ABSTRACTOR";
            updateTileStyle(fieldContainer, selectedOption.value);
            modifiedByDisplay.textContent = "ABSTRACTOR";
            updateContainerHighlight(container, patientData.procedureInfo);
          });
          fieldContainer.appendChild(select);
          const selectedOption = value.options.find(
            (option) => option.value === value.value
          );
          updateTileStyle(
            fieldContainer,
            selectedOption ? selectedOption.value : ""
          );
        } else if (value.input_type === "multi_select") {
          // Initialize the value as an array if it's not already
          if (!Array.isArray(value.value)) {
            value.value = value.value ? [value.value] : [];
          }

          // Create a simpler dropdown implementation
          // Create dropdown container with relative positioning
          const dropdownContainer = document.createElement("div");
          dropdownContainer.classList.add("dropdown-container");
          dropdownContainer.style.position = "relative";
          dropdownContainer.style.width = "100%";

          // Make sure all parent containers allow overflow
          fieldContainer.style.overflow = "visible";
          const tileWrapper = fieldContainer.parentElement;
          if (tileWrapper && tileWrapper.classList.contains("tile-wrapper")) {
            tileWrapper.style.overflow = "visible";
          }
          // Also set the container's overflow to visible
          container.style.overflow = "visible";

          // Create dropdown header/button
          const dropdownHeader = document.createElement("div");
          dropdownHeader.classList.add("dropdown-header");
          dropdownHeader.style.padding = "8px 12px";
          dropdownHeader.style.border = "1px solid #ccc";
          dropdownHeader.style.borderRadius = "4px";
          dropdownHeader.style.cursor = "pointer";
          dropdownHeader.style.display = "flex";
          dropdownHeader.style.justifyContent = "space-between";
          dropdownHeader.style.alignItems = "center";
          dropdownHeader.style.backgroundColor = "#fff";

          // Display selected values or placeholder
          const selectedText = document.createElement("span");
          selectedText.classList.add("selected-text");

          // Function to update the selected text display
          const updateSelectedText = () => {
            if (value.value.length === 0) {
              selectedText.textContent = "Select options...";
            } else if (value.value.length === 1) {
              const selectedOption = value.options.find(
                (opt) => opt.value === value.value[0]
              );
              selectedText.textContent = selectedOption
                ? selectedOption.value
                : value.value[0];
            } else {
              selectedText.textContent = `${value.value.length} options selected`;
            }
          };

          updateSelectedText();

          // Add dropdown arrow
          const dropdownArrow = document.createElement("span");
          dropdownArrow.innerHTML = "&#9662;"; // Down arrow character

          dropdownHeader.appendChild(selectedText);
          dropdownHeader.appendChild(dropdownArrow);

          // Create dropdown content (initially hidden)
          const dropdownContent = document.createElement("div");
          dropdownContent.classList.add("dropdown-content");
          dropdownContent.style.display = "none";
          // Position the dropdown directly under the header
          dropdownContent.style.position = "fixed"; // Use fixed positioning to ensure visibility
          dropdownContent.style.width = "350px"; // Fixed width to ensure content is visible
          dropdownContent.style.maxHeight = "200px";
          dropdownContent.style.overflowY = "auto";
          dropdownContent.style.overflowX = "hidden";
          dropdownContent.style.backgroundColor = "#fff";
          dropdownContent.style.border = "1px solid #ccc";
          dropdownContent.style.borderRadius = "4px";
          dropdownContent.style.boxShadow = "0 4px 8px rgba(0,0,0,0.3)";
          dropdownContent.style.zIndex = "9999"; // Very high z-index to ensure it's on top

          // Create a checkbox for each option
          value.options.forEach((option) => {
            const checkboxWrapper = document.createElement("div");
            checkboxWrapper.classList.add("checkbox-wrapper");
            checkboxWrapper.style.display = "flex";
            checkboxWrapper.style.alignItems = "center";
            checkboxWrapper.style.padding = "8px 12px";
            checkboxWrapper.style.borderBottom = "1px solid #eee";
            checkboxWrapper.style.textAlign = "left";
            checkboxWrapper.style.cursor = "pointer";

            const input = document.createElement("input");
            input.type = "checkbox";
            input.name = `${key}-${option.id}`;
            input.value = option.id;
            input.id = `${key}-${option.id}`;
            input.style.marginRight = "4px"; // Add a small space between checkbox and text

            // Check if this option is in the selected values array
            input.checked = value.value.includes(option.value);

            input.addEventListener("change", (e) => {
              // Get the current values array
              let currentValues = Array.isArray(value.value)
                ? [...value.value]
                : [];

              if (e.target.checked) {
                // Add the value if it's not already in the array
                if (!currentValues.includes(option.value)) {
                  currentValues.push(option.value);
                }
              } else {
                // Remove the value if it's in the array
                currentValues = currentValues.filter(
                  (val) => val !== option.value
                );
              }

              // Update the model
              patientData.procedureInfo[category][key].value = currentValues;
              patientData.procedureInfo[category][key].modified_by =
                "ABSTRACTOR";

              // Update selected text display
              updateSelectedText();

              // Update UI
              updateTileStyle(
                fieldContainer,
                currentValues.length > 0 ? "filled" : ""
              );
              modifiedByDisplay.textContent = "ABSTRACTOR";
              updateContainerHighlight(container, patientData.procedureInfo);
            });

            const optionLabel = document.createElement("label");
            optionLabel.setAttribute("for", `${key}-${option.id}`);
            optionLabel.style.marginLeft = "0";
            optionLabel.style.display = "inline-block";
            optionLabel.style.whiteSpace = "nowrap";
            optionLabel.style.cursor = "pointer";
            optionLabel.style.flexGrow = "1";

            // Display field_id in parentheses if available
            if (option.field_id) {
              optionLabel.innerText = `${option.value} (${option.field_id})`;
            } else {
              optionLabel.innerText = option.value;
            }

            // Create a wrapper for the checkbox and label to ensure they're tightly aligned
            const inputLabelWrapper = document.createElement("div");
            inputLabelWrapper.style.display = "flex";
            inputLabelWrapper.style.alignItems = "center";
            inputLabelWrapper.style.gap = "0";

            inputLabelWrapper.appendChild(input);
            inputLabelWrapper.appendChild(optionLabel);
            checkboxWrapper.appendChild(inputLabelWrapper);
            dropdownContent.appendChild(checkboxWrapper);
          });

          // Function to position the dropdown content properly
          const positionDropdown = () => {
            const headerRect = dropdownHeader.getBoundingClientRect();
            const viewportHeight = window.innerHeight;
            const viewportWidth = window.innerWidth;

            // Position dropdown below the header
            dropdownContent.style.top = `${headerRect.bottom}px`;

            // Ensure dropdown doesn't go off the right edge of the screen
            const rightEdge = headerRect.left + 250; // 250px is our dropdown width
            if (rightEdge > viewportWidth) {
              // Align to the right edge of the header instead
              dropdownContent.style.left = `${headerRect.right - 250}px`;
            } else {
              dropdownContent.style.left = `${headerRect.left}px`;
            }

            // Set max height based on available space
            const spaceBelow = viewportHeight - headerRect.bottom;
            const maxHeight = Math.max(100, Math.min(200, spaceBelow - 20));
            dropdownContent.style.maxHeight = `${maxHeight}px`;
          };

          // Close dropdown when clicking outside
          document.addEventListener("click", (e) => {
            if (
              !dropdownContainer.contains(e.target) &&
              !dropdownContent.contains(e.target)
            ) {
              dropdownContent.style.display = "none";
              dropdownArrow.innerHTML = "&#9662;";
            }
          });

          // Simple function to close the dropdown
          const closeDropdown = () => {
            dropdownContent.style.display = "none";
            dropdownArrow.innerHTML = "&#9662;";
            window.removeEventListener("scroll", closeDropdown);
          };

          // Clean up event listener when the component is removed
          const cleanupFunc = () => {
            if (document.body.contains(dropdownContent)) {
              document.body.removeChild(dropdownContent);
            }
            // Remove scroll event listener
            window.removeEventListener("scroll", closeDropdown);
          };

          // Store the cleanup function for potential future use
          dropdownContainer.cleanupFunc = cleanupFunc;

          // Add header to container, but add content to document body for better visibility
          dropdownContainer.appendChild(dropdownHeader);

          // Add the dropdown content to the document body when needed
          const showDropdown = () => {
            if (!document.body.contains(dropdownContent)) {
              document.body.appendChild(dropdownContent);
            }
            dropdownContent.style.display = "block";
            positionDropdown();
          };

          // Set up the click handler for the dropdown
          dropdownHeader.onclick = (e) => {
            e.stopPropagation();
            const isOpen = dropdownContent.style.display === "block";

            if (!isOpen) {
              showDropdown();
              dropdownArrow.innerHTML = "&#9652;"; // Up arrow
              window.addEventListener("scroll", closeDropdown);
            } else {
              closeDropdown();
            }
          };

          // Update the tile style based on whether any options are selected
          updateTileStyle(
            fieldContainer,
            value.value.length > 0 ? "filled" : ""
          );

          fieldContainer.appendChild(dropdownContainer);
        } else if (value.input_type === "multi_input_field") {
          const multiInputContainer = document.createElement("div");
          multiInputContainer.classList.add("multi-input-container");
          multiInputContainer.style.position = "relative";

          // Create radio buttons for options instead of text input and checkbox
          const radioContainer = document.createElement("div");
          radioContainer.classList.add("radio-container");
          radioContainer.style.display = "flex";
          radioContainer.style.flexDirection = "row";
          radioContainer.style.flexWrap = "nowrap"; // Prevent wrapping to keep in one line
          radioContainer.style.gap = "15px";
          radioContainer.style.width = "100%";

          // Create a single container for conditional content
          const conditionalContainer = document.createElement("div");
          conditionalContainer.classList.add("conditional-content-container");
          conditionalContainer.style.marginTop = "10px";
          conditionalContainer.style.display = "none"; // Initially hidden

          // Create radio buttons for each option
          value.options.forEach((option) => {
            const radioWrapper = document.createElement("div");
            radioWrapper.style.display = "flex";
            radioWrapper.style.alignItems = "center";

            const input = document.createElement("input");
            input.type = "radio";
            input.name = key;
            input.value = option;
            input.id = `${key}-${option}`;
            input.checked = value.value === option;

            // Update patientData and render conditional content when a radio is selected
            input.addEventListener("change", (e) => {
              if (e.target.checked) {
                patientData.procedureInfo[category][key].value = e.target.value;
                patientData.procedureInfo[category][key].modified_by =
                  "ABSTRACTOR";
                updateTileStyle(fieldContainer, e.target.value);
                modifiedByDisplay.textContent = "ABSTRACTOR";
                updateContainerHighlight(container, patientData.procedureInfo);

                // Clear values from other conditional sections when switching options
                if (e.target.value === "No" && value.if_yes) {
                  clearConditionalValues(value, "if_yes");
                } else if (e.target.value === "Yes" && value.if_no) {
                  clearConditionalValues(value, "if_no");
                } else if (e.target.value === "Deceased" && value.if_alive) {
                  clearConditionalValues(value, "if_alive");
                } else if (e.target.value === "Alive" && value.if_deceased) {
                  clearConditionalValues(value, "if_deceased");
                }

                // Always clear any previously rendered content first
                conditionalContainer.innerHTML = "";
                conditionalContainer.style.display = "none";

                // Determine which conditional content to show based on selected option
                let conditionalKey = null;
                if (option === "Yes" && value.if_yes) {
                  conditionalKey = "if_yes";
                } else if (option === "No" && value.if_no) {
                  conditionalKey = "if_no";
                } else if (option === "Alive" && value.if_alive) {
                  conditionalKey = "if_alive";
                } else if (option === "Deceased" && value.if_deceased) {
                  conditionalKey = "if_deceased";
                }

                // Render conditional content if available
                if (conditionalKey && value[conditionalKey]) {
                  renderConditionalContent(
                    option,
                    value,
                    conditionalContainer,
                    key
                  );
                }
              }
            });

            const optionLabel = document.createElement("label");
            optionLabel.setAttribute("for", `${key}-${option}`);
            optionLabel.innerText = option;
            if (value.description) {
              optionLabel.setAttribute("title", value.description);
            }

            radioWrapper.appendChild(input);
            radioWrapper.appendChild(optionLabel);
            radioContainer.appendChild(radioWrapper);
          });

          // Add radio container and conditional container to the multi-input container
          multiInputContainer.appendChild(radioContainer);
          multiInputContainer.appendChild(conditionalContainer);
          fieldContainer.appendChild(multiInputContainer);

          // If a value is already selected, render the conditional content
          if (value.value) {
            // Find the radio button for the selected value
            const selectedRadio = radioContainer.querySelector(
              `input[value="${value.value}"]`
            );
            if (selectedRadio) {
              // Mark the radio as checked
              selectedRadio.checked = true;

              // Render conditional content directly
              renderConditionalContent(
                value.value,
                value,
                conditionalContainer,
                key
              );
            }
          }

          // Apply initial border styling using the selected radio value
          updateTileStyle(fieldContainer, value.value || "");
        }

        // Append field container and modified_by display to the tile wrapper.
        tileWrapper.appendChild(fieldContainer);
        tileWrapper.appendChild(modifiedByDisplay);

        // Append the tile wrapper to the category container.
        categoryContainer.appendChild(tileWrapper);
      });

      // Render dependency fields as combined fields for this category
      Object.entries(fields).forEach(([fieldKey, value]) => {
        // Skip fields that don't have dependencies or are already rendered
        if (!value.dependency || !skipFields.has(`${category}-${fieldKey}`))
          return;

        // Find the dependent field (N/A checkbox)
        const dependentFieldKey = Object.keys(fields).find(
          (k) => fields[k].field_id === value.dependency
        );

        if (!dependentFieldKey) return;

        const dependentField = fields[dependentFieldKey];
        if (dependentField.input_type !== "checkbox") return;

        // Create an outer wrapper (tile-wrapper) for border highlight and modified_by display.
        const tileWrapper = document.createElement("div");
        tileWrapper.classList.add("tile-wrapper");
        tileWrapper.style.position = "relative";
        tileWrapper.style.marginBottom = "24px";

        // Create the field container for the field.
        const fieldContainer = document.createElement("div");
        fieldContainer.classList.add("field-container");

        // Create a label for the field with proper classes.
        const label = document.createElement("label");
        label.classList.add("label", "cursor-pointer");
        label.innerHTML = `${value.label} (${value.field_id})`;
        // Add tooltip with description if provided.
        if (value.description) {
          label.setAttribute("title", value.description);
        }
        fieldContainer.appendChild(label);

        // If a metric is available, insert it after the label.
        if (value.metric) {
          const metricLabel = document.createElement("span");
          metricLabel.classList.add("metric");
          metricLabel.textContent = ` (${value.metric})`;
          fieldContainer.appendChild(metricLabel);
        }

        // Create the modified_by display element.
        const modifiedByDisplay = document.createElement("span");
        modifiedByDisplay.style.display = "block";
        modifiedByDisplay.style.textAlign = "right";
        modifiedByDisplay.style.marginTop = "-10px";
        modifiedByDisplay.style.color = "#8143d9";
        modifiedByDisplay.style.fontSize = "12px";
        // Initialize modified_by if not set.
        if (!value.modified_by) {
          value.modified_by = "";
        }
        modifiedByDisplay.textContent = value.modified_by;

        // Create the input field
        const input = document.createElement("input");
        input.type = "text";
        input.name = fieldKey;
        input.placeholder = `Enter ${value.label}`;
        input.value = value.value || "";

        // Create the checkbox wrapper
        const checkboxWrapper = document.createElement("div");
        checkboxWrapper.style.marginTop = "8px";
        checkboxWrapper.style.display = "flex";
        checkboxWrapper.style.alignItems = "center";

        const checkbox = document.createElement("input");
        checkbox.type = "checkbox";
        checkbox.name = dependentFieldKey;
        checkbox.id = `${category}-${dependentFieldKey}-checkbox`;
        checkbox.checked = dependentField.value === "True";

        const checkboxLabel = document.createElement("label");
        checkboxLabel.setAttribute(
          "for",
          `${category}-${dependentFieldKey}-checkbox`
        );
        checkboxLabel.innerText =
          `${dependentField.label} (${dependentField.field_id})` || "N/A";
        checkboxLabel.style.marginLeft = "5px";

        checkboxWrapper.appendChild(checkbox);
        checkboxWrapper.appendChild(checkboxLabel);

        // Set initial state based on current values
        if (dependentField.value === "True") {
          input.disabled = true;
          input.value = "";
          patientData.procedureInfo[category][fieldKey].value = "";
        }

        updateTileStyle(
          fieldContainer,
          value.value || (dependentField.value === "True" ? "True" : "")
        );

        // Add event listeners to make them act like radio buttons
        let previousValue = input.value;

        input.addEventListener("input", (e) => {
          const currentValue = e.target.value;

          // Use validation utilities with temporary callback
          let isValid = false;
          validateStringInput(
            currentValue,
            value.field_id,
            (validatedValue) => {
              isValid = true;
              previousValue = validatedValue;

              // Update model and UI only if value is valid
              patientData.procedureInfo[category][fieldKey].value =
                validatedValue;
              patientData.procedureInfo[category][fieldKey].modified_by =
                "ABSTRACTOR";

              // If input has value, disable the checkbox
              if (validatedValue) {
                checkbox.checked = false;
                patientData.procedureInfo[category][dependentFieldKey].value =
                  "";
              }

              updateTileStyle(fieldContainer, validatedValue);
              modifiedByDisplay.textContent = "ABSTRACTOR";
              updateContainerHighlight(container, patientData.procedureInfo);

              // Update input value if validation modified it
              if (validatedValue !== currentValue) {
                e.target.value = validatedValue;
              }
            }
          );

          // If validation didn't accept the value, revert to previous valid value
          if (!isValid) {
            e.target.value = previousValue;
          }
        });

        checkbox.addEventListener("change", (e) => {
          const isChecked = e.target.checked;

          // Update the dependent field value
          patientData.procedureInfo[category][dependentFieldKey].value =
            isChecked ? "True" : "";
          patientData.procedureInfo[category][dependentFieldKey].modified_by =
            "ABSTRACTOR";

          // If checkbox is checked, disable and clear the input
          if (isChecked) {
            input.disabled = true;
            input.value = "";
            patientData.procedureInfo[category][fieldKey].value = "";
          } else {
            input.disabled = false;
          }

          updateTileStyle(fieldContainer, isChecked ? "True" : "");
          modifiedByDisplay.textContent = "ABSTRACTOR";
          updateContainerHighlight(container, patientData.procedureInfo);
        });

        // Append elements to the container
        fieldContainer.appendChild(input);
        fieldContainer.appendChild(checkboxWrapper);
        tileWrapper.appendChild(fieldContainer);
        tileWrapper.appendChild(modifiedByDisplay);
        categoryContainer.appendChild(tileWrapper);
      });
    } // Close the else block
    // Append the flattened category container to the main grid container.
    container.appendChild(categoryContainer);
  });

  // Integrate container-level verified checkbox.
  if (
    typeof patientData.procedureInfo.verified !== "object" ||
    patientData.procedureInfo.verified === null
  ) {
    patientData.procedureInfo.verified = {
      value: patientData.procedureInfo.verified || "False",
      modified_by: "",
    };
  }
  const verifiedData = patientData.procedureInfo.verified;
  // Create the container-level verified checkbox.
  const verifiedContainer = document.createElement("div");
  verifiedContainer.style.position = "absolute";
  verifiedContainer.style.bottom = "16px";
  verifiedContainer.style.right = "16px";
  verifiedContainer.style.display = "flex";
  verifiedContainer.style.alignItems = "center";

  const containerVerifiedCheckbox = document.createElement("input");
  containerVerifiedCheckbox.type = "checkbox";
  containerVerifiedCheckbox.id = "procedureInfo-verified-checkbox";
  containerVerifiedCheckbox.checked = verifiedData.value === "True";
  containerVerifiedCheckbox.style.width = "24px";
  containerVerifiedCheckbox.style.height = "24px";
  containerVerifiedCheckbox.addEventListener("change", (e) => {
    verifiedData.value = e.target.checked ? "True" : "False";
    updateContainerHighlight(container, patientData.procedureInfo);
  });

  const containerVerifiedLabel = document.createElement("label");
  containerVerifiedLabel.setAttribute("for", "procedureInfo-verified-checkbox");
  containerVerifiedLabel.classList.add("mt-2", "ml-2");
  containerVerifiedLabel.innerText = "Verified";
  containerVerifiedLabel.style.fontSize = "18px";
  containerVerifiedLabel.style.fontWeight = "bold";

  verifiedContainer.appendChild(containerVerifiedCheckbox);
  verifiedContainer.appendChild(containerVerifiedLabel);
  container.appendChild(verifiedContainer);

  // Initial update of container highlight.
  updateContainerHighlight(container, patientData.procedureInfo);
}
