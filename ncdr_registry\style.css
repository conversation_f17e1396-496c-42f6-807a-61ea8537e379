* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
  }

  body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #f8f6ff, #e6e2ec);
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
    color: #333;
    position: relative;
  }

  .container {
    width: 100%;
    max-width: 800px;
    background: white;
    padding: 30px;
    border-radius: 20px;
    box-shadow: 0 20px 60px rgba(129, 67, 217, 0.1);
    transition: all 0.3s ease-in-out;
    overflow-wrap: break-word;
    position: relative;
    z-index: 1;
  }

  .container:hover {
    transform: translateY(-2px);
    box-shadow: 0 25px 80px rgba(129, 67, 217, 0.15);
  }

  /* Header Styles */
  .header {
    text-align: center;
    margin-bottom: 40px;
    padding-bottom: 20px;
    border-bottom: 2px solid #f0f0f0;
  }

  .header-icon {
    font-size: 48px;
    color: #8143d9;
    margin-bottom: 15px;
    display: block;
  }

  h2 {
    font-size: 32px;
    margin-bottom: 10px;
    color: #8143d9;
    font-weight: 700;
  }

  .subtitle {
    color: #666;
    font-size: 16px;
    margin: 0;
  }

  /* Section Styles */
  .section {
    margin-bottom: 35px;
    padding: 25px;
    background: #fafafa;
    border-radius: 15px;
    border: 1px solid #f0f0f0;
    transition: all 0.3s ease;
  }

  .section:hover {
    background: #f8f6ff;
    border-color: #8143d9;
  }

  .section h3 {
    color: #8143d9;
    font-size: 18px;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 10px;
  }

  .section h3 i {
    font-size: 20px;
  }

  label {
    font-weight: 600;
    margin-bottom: 8px;
    display: block;
    color: #444;
    font-size: 14px;
  }

  /* Input Styles */
  .input-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
  }

  input[type="date"],
  select {
    width: 100%;
    padding: 15px;
    border-radius: 12px;
    font-size: 16px;
    border: 2px solid #e0e0e0;
    transition: all 0.3s ease;
    background: white;
  }

  input[type="date"]:focus,
  select:focus {
    border-color: #8143d9;
    outline: none;
    box-shadow: 0 0 0 3px rgba(129, 67, 217, 0.1);
    transform: translateY(-1px);
  }

  input[type="date"]:disabled,
  select:disabled {
    background: #f5f5f5;
    color: #999;
    cursor: not-allowed;
  }

  /* Select Wrapper */
  .select-wrapper {
    position: relative;
  }

  .select-arrow {
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: #8143d9;
    pointer-events: none;
    font-size: 14px;
  }

  /* Button Styles */
  .btn-primary,
  .btn-secondary,
  .btn-clear {
    padding: 15px 25px;
    border-radius: 12px;
    font-size: 16px;
    font-weight: 600;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    text-transform: none;
    letter-spacing: 0;
    min-height: 50px;
  }

  .btn-primary {
    background: linear-gradient(135deg, #8143d9, #6b2fb8);
    color: white;
    box-shadow: 0 4px 15px rgba(129, 67, 217, 0.3);
  }

  .btn-primary:hover:not(:disabled) {
    background: linear-gradient(135deg, #6b2fb8, #5a2599);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(129, 67, 217, 0.4);
  }

  .btn-secondary {
    background: linear-gradient(135deg, #f8f6ff, #e6e2ec);
    color: #8143d9;
    border: 2px solid #8143d9;
  }

  .btn-secondary:hover:not(:disabled) {
    background: linear-gradient(135deg, #8143d9, #6b2fb8);
    color: white;
    transform: translateY(-2px);
  }

  .btn-clear {
    background: linear-gradient(135deg, #ff6b6b, #ee5a52);
    color: white;
    margin-top: 15px;
  }

  .btn-clear:hover {
    background: linear-gradient(135deg, #ee5a52, #dc4c41);
    transform: translateY(-2px);
  }

  button:disabled {
    background: #e0e0e0 !important;
    color: #999 !important;
    cursor: not-allowed !important;
    transform: none !important;
    box-shadow: none !important;
  }

  /* Date Range Layout */
  .date-range {
    display: grid;
    grid-template-columns: 1fr 1fr auto;
    gap: 20px;
    align-items: end;
  }

  /* Button Group */
  .button-group {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
  }

  /* Table Header and Controls */
  .table-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    flex-wrap: wrap;
    gap: 15px;
  }

  .table-controls {
    display: flex;
    align-items: center;
    gap: 15px;
    flex-wrap: wrap;
  }

  .filter-controls {
    display: flex;
    gap: 10px;
  }

  .filter-select {
    padding: 8px 12px;
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    font-size: 14px;
    background: white;
    min-width: 150px;
  }

  .filter-select:focus {
    border-color: #8143d9;
    outline: none;
  }

  .table-actions {
    display: flex;
    align-items: center;
    gap: 10px;
  }

  .selected-count {
    font-size: 14px;
    color: #8143d9;
    font-weight: 600;
  }

  .btn-small {
    padding: 8px 16px;
    font-size: 14px;
    min-height: auto;
  }

  /* Table Container */
  .table-container {
    background: white;
    border-radius: 15px;
    box-shadow: 0 8px 25px rgba(129, 67, 217, 0.1);
    overflow: hidden;
    border: 2px solid #f0f0f0;
  }

  /* No Data Message */
  .no-data-message {
    text-align: center;
    padding: 60px 20px;
    color: #666;
  }

  .no-data-message i {
    font-size: 48px;
    color: #8143d9;
    margin-bottom: 15px;
    display: block;
  }

  .no-data-message h4 {
    font-size: 20px;
    margin-bottom: 10px;
    color: #333;
  }

  .no-data-message p {
    font-size: 16px;
    margin: 0;
  }

  /* Patients Table */
  .patients-table {
    width: 100%;
    border-collapse: collapse;
    background: white;
  }

  .patients-table thead {
    background: linear-gradient(135deg, #8143d9, #6b2fb8);
    color: white;
  }

  .patients-table thead th {
    padding: 15px 12px;
    text-align: left;
    font-weight: 600;
    font-size: 14px;
    border: none;
    position: relative;
  }

  .patients-table thead th.sortable {
    cursor: pointer;
    user-select: none;
  }

  .patients-table thead th.sortable:hover {
    background: rgba(255, 255, 255, 0.1);
  }

  .patients-table thead th .sort-icon {
    margin-left: 5px;
    opacity: 0.6;
  }

  .patients-table thead th.sort-asc .sort-icon:before {
    content: "▲";
  }

  .patients-table thead th.sort-desc .sort-icon:before {
    content: "▼";
  }

  .patients-table tbody tr {
    border-bottom: 1px solid #f0f0f0;
    transition: all 0.2s ease;
  }

  .patients-table tbody tr:hover {
    background-color: #f8f6ff;
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(129, 67, 217, 0.1);
  }

  .patients-table tbody tr.selected {
    background-color: #e8f5e8;
    border-color: #4caf50;
  }

  .patients-table tbody td {
    padding: 15px 12px;
    vertical-align: middle;
    font-size: 14px;
    border: none;
  }

  /* Checkbox Column */
  .checkbox-cell {
    width: 50px;
    text-align: center;
  }

  .row-checkbox {
    width: 18px;
    height: 18px;
    cursor: pointer;
  }

  /* Patient Name Column */
  .patient-name {
    font-weight: 600;
    color: #333;
  }

  .patient-id {
    font-size: 12px;
    color: #666;
    margin-top: 2px;
  }

  /* Case ID Column */
  .case-id {
    font-family: 'Courier New', monospace;
    font-weight: 600;
    color: #8143d9;
    cursor: pointer;
  }

  .case-id:hover {
    text-decoration: underline;
  }

  /* Status Badges */
  .status-badge {
    display: inline-flex;
    align-items: center;
    gap: 5px;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }

  .status-verified {
    background: linear-gradient(135deg, #e8f5e8, #f0f8f0);
    color: #2e7d32;
    border: 1px solid #4caf50;
  }

  .status-not-verified {
    background: linear-gradient(135deg, #ffebee, #fce4ec);
    color: #c62828;
    border: 1px solid #f44336;
  }

  .status-submitted {
    background: linear-gradient(135deg, #e3f2fd, #f1f8ff);
    color: #1565c0;
    border: 1px solid #2196f3;
  }

  .status-pending {
    background: linear-gradient(135deg, #fff3e0, #fef7ed);
    color: #ef6c00;
    border: 1px solid #ff9800;
  }

  .status-failed {
    background: linear-gradient(135deg, #ffebee, #fce4ec);
    color: #c62828;
    border: 1px solid #f44336;
  }

  .status-not-sent {
    background: linear-gradient(135deg, #f5f5f5, #fafafa);
    color: #757575;
    border: 1px solid #bdbdbd;
  }

  /* Action Buttons */
  .action-buttons {
    display: flex;
    gap: 8px;
    justify-content: center;
  }

  .action-btn {
    padding: 6px 10px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 12px;
    font-weight: 600;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 4px;
    min-width: 70px;
    justify-content: center;
  }

  .action-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }

  .action-btn.submit {
    background: linear-gradient(135deg, #8143d9, #6b2fb8);
    color: white;
  }

  .action-btn.status {
    background: linear-gradient(135deg, #17a2b8, #138496);
    color: white;
  }

  .action-btn.details {
    background: linear-gradient(135deg, #6c757d, #5a6268);
    color: white;
  }

  .action-btn:disabled {
    background: #e0e0e0 !important;
    color: #999 !important;
    cursor: not-allowed !important;
    transform: none !important;
    box-shadow: none !important;
  }

  /* Bulk Actions */
  .bulk-actions {
    margin-top: 20px;
    padding: 15px 20px;
    background: linear-gradient(135deg, #f8f6ff, #ffffff);
    border-radius: 12px;
    border: 2px solid #8143d9;
    box-shadow: 0 4px 15px rgba(129, 67, 217, 0.1);
  }

  .bulk-actions-content {
    display: flex;
    align-items: center;
    gap: 15px;
    flex-wrap: wrap;
  }

  .bulk-label {
    font-weight: 600;
    color: #8143d9;
    font-size: 16px;
  }

  /* Last Submission Column */
  .last-submission {
    font-size: 12px;
    color: #666;
  }

  .submission-time {
    font-weight: 500;
  }

  .submission-status {
    margin-top: 2px;
    font-size: 11px;
  }

  /* Loading State for Table */
  .table-loading {
    text-align: center;
    padding: 40px 20px;
    color: #666;
  }

  .table-loading .loader {
    margin: 0 auto 15px;
  }

  /* Empty State */
  .empty-state {
    text-align: center;
    padding: 40px 20px;
    color: #666;
  }

  .empty-state i {
    font-size: 48px;
    color: #8143d9;
    margin-bottom: 15px;
    display: block;
  }

  .empty-state h4 {
    font-size: 18px;
    margin-bottom: 10px;
    color: #333;
  }

  .empty-state p {
    font-size: 14px;
    margin: 0;
  }
  /* Patient Info */
  .patient-info {
    margin-top: 15px;
    padding: 20px;
    background: linear-gradient(135deg, #f8f6ff, #ffffff);
    border-radius: 15px;
    border: 2px solid #8143d9;
    box-shadow: 0 8px 25px rgba(129, 67, 217, 0.1);
  }

  .patient-details-container h4 {
    color: #8143d9;
    margin-bottom: 20px;
    font-size: 20px;
    display: flex;
    align-items: center;
    gap: 10px;
    padding-bottom: 10px;
    border-bottom: 2px solid #f0f0f0;
  }

  .patient-details-container h4 i {
    font-size: 22px;
  }

  /* Patient Details Table */
  .patient-details-table {
    width: 100%;
    border-collapse: collapse;
    margin: 20px 0;
    background: white;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
  }

  .patient-details-table tbody tr {
    border-bottom: 1px solid #f0f0f0;
    transition: background-color 0.2s ease;
  }

  .patient-details-table tbody tr:hover {
    background-color: #f8f6ff;
  }

  .patient-details-table tbody tr:last-child {
    border-bottom: none;
  }

  .patient-details-table .label-cell {
    font-weight: 600;
    color: #555;
    padding: 12px 15px;
    background-color: #fafafa;
    border-right: 1px solid #f0f0f0;
    width: 35%;
    vertical-align: top;
  }

  .patient-details-table .value-cell {
    padding: 12px 15px;
    color: #333;
    word-break: break-word;
  }

  .patient-details-table .value-cell code {
    background: #f5f5f5;
    padding: 4px 8px;
    border-radius: 4px;
    font-family: 'Courier New', monospace;
    font-size: 13px;
    color: #8143d9;
    border: 1px solid #e0e0e0;
  }

  .patient-details-table .verified {
    color: #4caf50;
    font-weight: 600;
  }

  .patient-details-table .not-verified {
    color: #f44336;
    font-weight: 600;
  }

  /* History Header */
  .history-header {
    color: #8143d9;
    font-size: 18px;
    margin: 25px 0 15px 0;
    display: flex;
    align-items: center;
    gap: 10px;
    padding-bottom: 8px;
    border-bottom: 2px solid #f0f0f0;
  }

  .history-header i {
    font-size: 16px;
  }

  /* Submission History Table */
  .submission-history-table {
    width: 100%;
    border-collapse: collapse;
    margin: 15px 0;
    background: white;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
  }

  .submission-history-table thead {
    background: linear-gradient(135deg, #8143d9, #6b2fb8);
    color: white;
  }

  .submission-history-table thead th {
    padding: 15px 12px;
    text-align: left;
    font-weight: 600;
    font-size: 14px;
    border-right: 1px solid rgba(255, 255, 255, 0.2);
  }

  .submission-history-table thead th:last-child {
    border-right: none;
  }

  .submission-history-table tbody tr {
    border-bottom: 1px solid #f0f0f0;
    transition: all 0.2s ease;
    cursor: pointer;
  }

  .submission-history-table tbody tr:hover {
    background-color: #f8f6ff;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(129, 67, 217, 0.1);
  }

  .submission-history-table tbody tr:last-child {
    border-bottom: none;
  }

  .submission-history-table tbody td {
    padding: 12px;
    color: #333;
    font-size: 14px;
    vertical-align: middle;
  }

  .submission-history-table tbody td code {
    background: #f5f5f5;
    padding: 4px 8px;
    border-radius: 4px;
    font-family: 'Courier New', monospace;
    font-size: 12px;
    color: #8143d9;
    border: 1px solid #e0e0e0;
  }

  .submission-history-table tbody tr[data-index] {
    position: relative;
  }

  .submission-history-table tbody tr[data-index]:hover::after {
    content: "Click to view details";
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    background: #8143d9;
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 11px;
    opacity: 0.8;
  }

  /* Empty state for submission history */
  .submission-history-table tbody td[colspan] {
    text-align: center;
    padding: 30px;
    color: #999;
    font-style: italic;
    background: #fafafa;
  }

  /* Results Container */
  .result-container {
    padding: 20px;
    border-radius: 12px;
    font-size: 14px;
    line-height: 1.6;
    overflow: auto;
    word-wrap: break-word;
    word-break: break-word;
    white-space: pre-wrap;
    max-height: 400px;
  }

  .result-success {
    background: linear-gradient(135deg, #e8f5e8, #f0f8f0);
    border: 2px solid #4caf50;
    color: #2e7d32;
  }

  .result-error {
    background: linear-gradient(135deg, #ffebee, #fce4ec);
    border: 2px solid #f44336;
    color: #c62828;
  }

  .result-info {
    background: linear-gradient(135deg, #f3f1f7, #f8f6ff);
    border: 2px solid #8143d9;
    color: #5e1cba;
  }

  /* Loading Overlay */
  .loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.95);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 10000;
    backdrop-filter: blur(5px);
  }

  .loading-content {
    text-align: center;
    padding: 30px;
    background: white;
    border-radius: 20px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
  }

  .loading-content p {
    margin-top: 20px;
    color: #8143d9;
    font-size: 16px;
    font-weight: 600;
  }

  /* Loader (from abstractor) */
  .loader {
    border: 5px solid #f3f3f3;
    border-top: 5px solid #8143d9;
    border-radius: 50%;
    width: 60px;
    height: 60px;
    animation: spin 1s linear infinite;
    margin: 0 auto;
    box-shadow: 0 0 10px rgba(129, 67, 217, 0.3);
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

  /* Toast Notifications */
  .toast {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 15px 20px;
    border-radius: 10px;
    color: white;
    font-weight: 600;
    z-index: 10001;
    transform: translateX(400px);
    transition: all 0.3s ease;
    max-width: 300px;
  }

  .toast.show {
    transform: translateX(0);
  }

  .toast.success {
    background: linear-gradient(135deg, #4caf50, #45a049);
  }

  .toast.error {
    background: linear-gradient(135deg, #f44336, #e53935);
  }

  .toast.info {
    background: linear-gradient(135deg, #8143d9, #6b2fb8);
  }

  /* Custom Modal Styles */
  .modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.6);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 10002;
    backdrop-filter: blur(5px);
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  .modal-overlay.show {
    opacity: 1;
  }

  .modal-container {
    background: white;
    border-radius: 20px;
    box-shadow: 0 25px 80px rgba(0, 0, 0, 0.3);
    max-width: 600px;
    width: 90%;
    max-height: 80vh;
    overflow: hidden;
    transform: scale(0.7);
    transition: transform 0.3s ease;
  }

  .modal-overlay.show .modal-container {
    transform: scale(1);
  }

  .modal-header {
    background: linear-gradient(135deg, #8143d9, #6b2fb8);
    color: white;
    padding: 20px 25px;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .modal-header h3 {
    margin: 0;
    font-size: 20px;
    display: flex;
    align-items: center;
    gap: 10px;
  }

  .modal-close-btn {
    background: none;
    border: none;
    color: white;
    font-size: 18px;
    cursor: pointer;
    padding: 5px;
    border-radius: 50%;
    width: 35px;
    height: 35px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.2s ease;
  }

  .modal-close-btn:hover {
    background: rgba(255, 255, 255, 0.2);
  }

  .modal-body {
    padding: 25px;
    max-height: 50vh;
    overflow-y: auto;
  }

  .modal-footer {
    padding: 20px 25px;
    border-top: 1px solid #f0f0f0;
    display: flex;
    justify-content: flex-end;
  }

  .modal-footer .btn-primary {
    min-width: 100px;
  }

  /* Modal Content Styles */
  .submission-detail-card {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  }

  .submission-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 2px solid #f0f0f0;
  }

  .status-badge {
    padding: 8px 16px;
    border-radius: 20px;
    font-weight: 600;
    font-size: 14px;
  }

  .status-success {
    background: linear-gradient(135deg, #e8f5e8, #f0f8f0);
    color: #2e7d32;
    border: 1px solid #4caf50;
  }

  .status-error {
    background: linear-gradient(135deg, #ffebee, #fce4ec);
    color: #c62828;
    border: 1px solid #f44336;
  }

  .status-pending {
    background: linear-gradient(135deg, #fff3e0, #fef7ed);
    color: #ef6c00;
    border: 1px solid #ff9800;
  }

  .status-default {
    background: linear-gradient(135deg, #f3f1f7, #f8f6ff);
    color: #5e1cba;
    border: 1px solid #8143d9;
  }

  .submission-date {
    color: #666;
    font-size: 14px;
    font-weight: 500;
  }

  .submission-info,
  .assessment-results,
  .error-info,
  .raw-result {
    margin-bottom: 20px;
  }

  .assessment-results h4,
  .error-info h4,
  .raw-result h4 {
    color: #8143d9;
    margin-bottom: 15px;
    font-size: 16px;
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .info-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 0;
    border-bottom: 1px solid #f5f5f5;
  }

  .info-row:last-child {
    border-bottom: none;
  }

  .info-label {
    font-weight: 600;
    color: #555;
    flex: 1;
  }

  .info-value {
    color: #333;
    font-weight: 500;
    text-align: right;
    flex: 1;
  }

  .info-value.success {
    color: #2e7d32;
  }

  .info-value.error {
    color: #c62828;
  }

  .result-text {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 15px;
    font-size: 12px;
    line-height: 1.4;
    overflow-x: auto;
    white-space: pre-wrap;
    word-break: break-word;
  }

  /* Case Details Modal */
  .case-details {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  }

  .detail-section {
    margin-bottom: 25px;
    padding-bottom: 20px;
    border-bottom: 1px solid #f0f0f0;
  }

  .detail-section:last-child {
    border-bottom: none;
    margin-bottom: 0;
  }

  .detail-section h4 {
    color: #8143d9;
    margin-bottom: 15px;
    font-size: 16px;
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .detail-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
  }

  .detail-item {
    display: flex;
    flex-direction: column;
    gap: 5px;
  }

  .detail-label {
    font-weight: 600;
    color: #555;
    font-size: 14px;
  }

  .detail-value {
    color: #333;
    font-size: 14px;
  }

  .verification-status {
    display: flex;
    align-items: center;
  }

  /* Bulk Status Results */
  .bulk-status-results h4 {
    color: #8143d9;
    margin-bottom: 20px;
    font-size: 18px;
  }

  .results-list {
    max-height: 400px;
    overflow-y: auto;
  }

  .result-item {
    padding: 15px;
    margin-bottom: 10px;
    border-radius: 8px;
    border-left: 4px solid;
  }

  .result-item.success {
    background: #f8fff8;
    border-left-color: #4caf50;
  }

  .result-item.error {
    background: #fff8f8;
    border-left-color: #f44336;
  }

  .status-result {
    background: #f5f5f5;
    padding: 10px;
    border-radius: 4px;
    font-size: 11px;
    margin-top: 8px;
    overflow-x: auto;
  }

  .error-text {
    color: #c62828;
    font-weight: 500;
  }

  /* Responsive Design */
  @media (max-width: 768px) {
    body {
      padding: 10px;
    }

    .container {
      padding: 20px;
      border-radius: 15px;
    }

    .header-icon {
      font-size: 36px;
    }

    h2 {
      font-size: 24px;
    }

    .subtitle {
      font-size: 14px;
    }

    .section {
      padding: 20px;
      margin-bottom: 25px;
    }

    .section h3 {
      font-size: 16px;
    }

    .date-range {
      grid-template-columns: 1fr;
      gap: 15px;
    }

    .button-group {
      grid-template-columns: 1fr;
      gap: 10px;
    }

    .btn-primary,
    .btn-secondary,
    .btn-clear {
      padding: 12px 20px;
      font-size: 14px;
      min-height: 45px;
    }

    .toast {
      top: 10px;
      right: 10px;
      left: 10px;
      max-width: none;
      transform: translateY(-100px);
    }

    .toast.show {
      transform: translateY(0);
    }

    .loading-content {
      padding: 20px;
      margin: 0 20px;
    }

    .loader {
      width: 50px;
      height: 50px;
    }

    /* Tablet responsive for patient tables */
    .patient-details-table .label-cell {
      width: 35%;
    }

    .submission-history-table {
      font-size: 13px;
    }

    .submission-history-table thead th,
    .submission-history-table tbody td {
      padding: 10px 8px;
    }

    .modal-container {
      width: 95%;
      max-height: 85vh;
    }

    .modal-header {
      padding: 15px 20px;
    }

    .modal-header h3 {
      font-size: 18px;
    }

    .modal-body {
      padding: 20px;
      max-height: 60vh;
    }

    .modal-footer {
      padding: 15px 20px;
    }

    /* Table Responsive */
    .table-header {
      flex-direction: column;
      align-items: stretch;
    }

    .table-controls {
      justify-content: space-between;
    }

    .filter-controls {
      flex: 1;
    }

    .filter-select {
      min-width: 120px;
    }

    .patients-table {
      font-size: 12px;
    }

    .patients-table thead th,
    .patients-table tbody td {
      padding: 10px 8px;
    }

    .action-buttons {
      flex-direction: column;
      gap: 4px;
    }

    .action-btn {
      font-size: 11px;
      padding: 4px 8px;
      min-width: 60px;
    }

    .bulk-actions-content {
      flex-direction: column;
      align-items: stretch;
      gap: 10px;
    }

    .bulk-actions-content .btn-small {
      width: 100%;
      justify-content: center;
    }
  }

  @media (max-width: 480px) {
    .container {
      padding: 15px;
    }

    .section {
      padding: 15px;
    }

    .btn-primary,
    .btn-secondary,
    .btn-clear {
      padding: 10px 15px;
      font-size: 13px;
      min-height: 40px;
    }

    .result-container {
      font-size: 12px;
      max-height: 300px;
    }

    /* Mobile responsive for patient tables */
    .patient-details-table,
    .submission-history-table {
      font-size: 12px;
    }

    .patient-details-table .label-cell,
    .patient-details-table .value-cell {
      padding: 8px 10px;
    }

    .patient-details-table .label-cell {
      width: 40%;
    }

    .submission-history-table thead th,
    .submission-history-table tbody td {
      padding: 8px 6px;
      font-size: 11px;
    }

    .submission-history-table tbody tr:hover::after {
      display: none;
    }
  }

  /* Animation Classes */
  .fade-in {
    animation: fadeIn 0.5s ease-in;
  }

  .fade-out {
    animation: fadeOut 0.3s ease-out;
  }

  @keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
  }

  @keyframes fadeOut {
    from { opacity: 1; transform: translateY(0); }
    to { opacity: 0; transform: translateY(-20px); }
  }

  /* Pulse animation for loading states */
  .pulse {
    animation: pulse 2s infinite;
  }

  @keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
  }