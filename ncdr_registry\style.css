* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
  }

  body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #f8f6ff, #e6e2ec);
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
    color: #333;
    position: relative;
  }

  .container {
    width: 100%;
    max-width: 800px;
    background: white;
    padding: 30px;
    border-radius: 20px;
    box-shadow: 0 20px 60px rgba(129, 67, 217, 0.1);
    transition: all 0.3s ease-in-out;
    overflow-wrap: break-word;
    position: relative;
    z-index: 1;
  }

  .container:hover {
    transform: translateY(-2px);
    box-shadow: 0 25px 80px rgba(129, 67, 217, 0.15);
  }

  /* Header Styles */
  .header {
    text-align: center;
    margin-bottom: 40px;
    padding-bottom: 20px;
    border-bottom: 2px solid #f0f0f0;
  }

  .header-icon {
    font-size: 48px;
    color: #8143d9;
    margin-bottom: 15px;
    display: block;
  }

  h2 {
    font-size: 32px;
    margin-bottom: 10px;
    color: #8143d9;
    font-weight: 700;
  }

  .subtitle {
    color: #666;
    font-size: 16px;
    margin: 0;
  }

  /* Section Styles */
  .section {
    margin-bottom: 35px;
    padding: 25px;
    background: #fafafa;
    border-radius: 15px;
    border: 1px solid #f0f0f0;
    transition: all 0.3s ease;
  }

  .section:hover {
    background: #f8f6ff;
    border-color: #8143d9;
  }

  .section h3 {
    color: #8143d9;
    font-size: 18px;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 10px;
  }

  .section h3 i {
    font-size: 20px;
  }

  label {
    font-weight: 600;
    margin-bottom: 8px;
    display: block;
    color: #444;
    font-size: 14px;
  }

  /* Input Styles */
  .input-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
  }

  input[type="date"],
  select {
    width: 100%;
    padding: 15px;
    border-radius: 12px;
    font-size: 16px;
    border: 2px solid #e0e0e0;
    transition: all 0.3s ease;
    background: white;
  }

  input[type="date"]:focus,
  select:focus {
    border-color: #8143d9;
    outline: none;
    box-shadow: 0 0 0 3px rgba(129, 67, 217, 0.1);
    transform: translateY(-1px);
  }

  input[type="date"]:disabled,
  select:disabled {
    background: #f5f5f5;
    color: #999;
    cursor: not-allowed;
  }

  /* Select Wrapper */
  .select-wrapper {
    position: relative;
  }

  .select-arrow {
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: #8143d9;
    pointer-events: none;
    font-size: 14px;
  }

  /* Button Styles */
  .btn-primary,
  .btn-secondary,
  .btn-clear {
    padding: 15px 25px;
    border-radius: 12px;
    font-size: 16px;
    font-weight: 600;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    text-transform: none;
    letter-spacing: 0;
    min-height: 50px;
  }

  .btn-primary {
    background: linear-gradient(135deg, #8143d9, #6b2fb8);
    color: white;
    box-shadow: 0 4px 15px rgba(129, 67, 217, 0.3);
  }

  .btn-primary:hover:not(:disabled) {
    background: linear-gradient(135deg, #6b2fb8, #5a2599);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(129, 67, 217, 0.4);
  }

  .btn-secondary {
    background: linear-gradient(135deg, #f8f6ff, #e6e2ec);
    color: #8143d9;
    border: 2px solid #8143d9;
  }

  .btn-secondary:hover:not(:disabled) {
    background: linear-gradient(135deg, #8143d9, #6b2fb8);
    color: white;
    transform: translateY(-2px);
  }

  .btn-clear {
    background: linear-gradient(135deg, #ff6b6b, #ee5a52);
    color: white;
    margin-top: 15px;
  }

  .btn-clear:hover {
    background: linear-gradient(135deg, #ee5a52, #dc4c41);
    transform: translateY(-2px);
  }

  button:disabled {
    background: #e0e0e0 !important;
    color: #999 !important;
    cursor: not-allowed !important;
    transform: none !important;
    box-shadow: none !important;
  }

  /* Date Range Layout */
  .date-range {
    display: grid;
    grid-template-columns: 1fr 1fr auto;
    gap: 20px;
    align-items: end;
  }

  /* Button Group */
  .button-group {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
  }
  /* Patient Info */
  .patient-info {
    margin-top: 15px;
    padding: 20px;
    background: linear-gradient(135deg, #f8f6ff, #ffffff);
    border-radius: 15px;
    border: 2px solid #8143d9;
    box-shadow: 0 8px 25px rgba(129, 67, 217, 0.1);
  }

  .patient-details-container h4 {
    color: #8143d9;
    margin-bottom: 20px;
    font-size: 20px;
    display: flex;
    align-items: center;
    gap: 10px;
    padding-bottom: 10px;
    border-bottom: 2px solid #f0f0f0;
  }

  .patient-details-container h4 i {
    font-size: 22px;
  }

  /* Patient Details Table */
  .patient-details-table {
    width: 100%;
    border-collapse: collapse;
    margin: 20px 0;
    background: white;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
  }

  .patient-details-table tbody tr {
    border-bottom: 1px solid #f0f0f0;
    transition: background-color 0.2s ease;
  }

  .patient-details-table tbody tr:hover {
    background-color: #f8f6ff;
  }

  .patient-details-table tbody tr:last-child {
    border-bottom: none;
  }

  .patient-details-table .label-cell {
    font-weight: 600;
    color: #555;
    padding: 12px 15px;
    background-color: #fafafa;
    border-right: 1px solid #f0f0f0;
    width: 35%;
    vertical-align: top;
  }

  .patient-details-table .value-cell {
    padding: 12px 15px;
    color: #333;
    word-break: break-word;
  }

  .patient-details-table .value-cell code {
    background: #f5f5f5;
    padding: 4px 8px;
    border-radius: 4px;
    font-family: 'Courier New', monospace;
    font-size: 13px;
    color: #8143d9;
    border: 1px solid #e0e0e0;
  }

  .patient-details-table .verified {
    color: #4caf50;
    font-weight: 600;
  }

  .patient-details-table .not-verified {
    color: #f44336;
    font-weight: 600;
  }

  /* History Header */
  .history-header {
    color: #8143d9;
    font-size: 18px;
    margin: 25px 0 15px 0;
    display: flex;
    align-items: center;
    gap: 10px;
    padding-bottom: 8px;
    border-bottom: 2px solid #f0f0f0;
  }

  .history-header i {
    font-size: 16px;
  }

  /* Submission History Table */
  .submission-history-table {
    width: 100%;
    border-collapse: collapse;
    margin: 15px 0;
    background: white;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
  }

  .submission-history-table thead {
    background: linear-gradient(135deg, #8143d9, #6b2fb8);
    color: white;
  }

  .submission-history-table thead th {
    padding: 15px 12px;
    text-align: left;
    font-weight: 600;
    font-size: 14px;
    border-right: 1px solid rgba(255, 255, 255, 0.2);
  }

  .submission-history-table thead th:last-child {
    border-right: none;
  }

  .submission-history-table tbody tr {
    border-bottom: 1px solid #f0f0f0;
    transition: all 0.2s ease;
    cursor: pointer;
  }

  .submission-history-table tbody tr:hover {
    background-color: #f8f6ff;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(129, 67, 217, 0.1);
  }

  .submission-history-table tbody tr:last-child {
    border-bottom: none;
  }

  .submission-history-table tbody td {
    padding: 12px;
    color: #333;
    font-size: 14px;
    vertical-align: middle;
  }

  .submission-history-table tbody td code {
    background: #f5f5f5;
    padding: 4px 8px;
    border-radius: 4px;
    font-family: 'Courier New', monospace;
    font-size: 12px;
    color: #8143d9;
    border: 1px solid #e0e0e0;
  }

  .submission-history-table tbody tr[data-index] {
    position: relative;
  }

  .submission-history-table tbody tr[data-index]:hover::after {
    content: "Click to view details";
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    background: #8143d9;
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 11px;
    opacity: 0.8;
  }

  /* Empty state for submission history */
  .submission-history-table tbody td[colspan] {
    text-align: center;
    padding: 30px;
    color: #999;
    font-style: italic;
    background: #fafafa;
  }

  /* Results Container */
  .result-container {
    padding: 20px;
    border-radius: 12px;
    font-size: 14px;
    line-height: 1.6;
    overflow: auto;
    word-wrap: break-word;
    word-break: break-word;
    white-space: pre-wrap;
    max-height: 400px;
  }

  .result-success {
    background: linear-gradient(135deg, #e8f5e8, #f0f8f0);
    border: 2px solid #4caf50;
    color: #2e7d32;
  }

  .result-error {
    background: linear-gradient(135deg, #ffebee, #fce4ec);
    border: 2px solid #f44336;
    color: #c62828;
  }

  .result-info {
    background: linear-gradient(135deg, #f3f1f7, #f8f6ff);
    border: 2px solid #8143d9;
    color: #5e1cba;
  }

  /* Loading Overlay */
  .loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.95);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 10000;
    backdrop-filter: blur(5px);
  }

  .loading-content {
    text-align: center;
    padding: 30px;
    background: white;
    border-radius: 20px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
  }

  .loading-content p {
    margin-top: 20px;
    color: #8143d9;
    font-size: 16px;
    font-weight: 600;
  }

  /* Loader (from abstractor) */
  .loader {
    border: 5px solid #f3f3f3;
    border-top: 5px solid #8143d9;
    border-radius: 50%;
    width: 60px;
    height: 60px;
    animation: spin 1s linear infinite;
    margin: 0 auto;
    box-shadow: 0 0 10px rgba(129, 67, 217, 0.3);
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

  /* Toast Notifications */
  .toast {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 15px 20px;
    border-radius: 10px;
    color: white;
    font-weight: 600;
    z-index: 10001;
    transform: translateX(400px);
    transition: all 0.3s ease;
    max-width: 300px;
  }

  .toast.show {
    transform: translateX(0);
  }

  .toast.success {
    background: linear-gradient(135deg, #4caf50, #45a049);
  }

  .toast.error {
    background: linear-gradient(135deg, #f44336, #e53935);
  }

  .toast.info {
    background: linear-gradient(135deg, #8143d9, #6b2fb8);
  }

  /* Responsive Design */
  @media (max-width: 768px) {
    body {
      padding: 10px;
    }

    .container {
      padding: 20px;
      border-radius: 15px;
    }

    .header-icon {
      font-size: 36px;
    }

    h2 {
      font-size: 24px;
    }

    .subtitle {
      font-size: 14px;
    }

    .section {
      padding: 20px;
      margin-bottom: 25px;
    }

    .section h3 {
      font-size: 16px;
    }

    .date-range {
      grid-template-columns: 1fr;
      gap: 15px;
    }

    .button-group {
      grid-template-columns: 1fr;
      gap: 10px;
    }

    .btn-primary,
    .btn-secondary,
    .btn-clear {
      padding: 12px 20px;
      font-size: 14px;
      min-height: 45px;
    }

    .toast {
      top: 10px;
      right: 10px;
      left: 10px;
      max-width: none;
      transform: translateY(-100px);
    }

    .toast.show {
      transform: translateY(0);
    }

    .loading-content {
      padding: 20px;
      margin: 0 20px;
    }

    .loader {
      width: 50px;
      height: 50px;
    }

    /* Tablet responsive for patient tables */
    .patient-details-table .label-cell {
      width: 35%;
    }

    .submission-history-table {
      font-size: 13px;
    }

    .submission-history-table thead th,
    .submission-history-table tbody td {
      padding: 10px 8px;
    }
  }

  @media (max-width: 480px) {
    .container {
      padding: 15px;
    }

    .section {
      padding: 15px;
    }

    .btn-primary,
    .btn-secondary,
    .btn-clear {
      padding: 10px 15px;
      font-size: 13px;
      min-height: 40px;
    }

    .result-container {
      font-size: 12px;
      max-height: 300px;
    }

    /* Mobile responsive for patient tables */
    .patient-details-table,
    .submission-history-table {
      font-size: 12px;
    }

    .patient-details-table .label-cell,
    .patient-details-table .value-cell {
      padding: 8px 10px;
    }

    .patient-details-table .label-cell {
      width: 40%;
    }

    .submission-history-table thead th,
    .submission-history-table tbody td {
      padding: 8px 6px;
      font-size: 11px;
    }

    .submission-history-table tbody tr:hover::after {
      display: none;
    }
  }

  /* Animation Classes */
  .fade-in {
    animation: fadeIn 0.5s ease-in;
  }

  .fade-out {
    animation: fadeOut 0.3s ease-out;
  }

  @keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
  }

  @keyframes fadeOut {
    from { opacity: 1; transform: translateY(0); }
    to { opacity: 0; transform: translateY(-20px); }
  }

  /* Pulse animation for loading states */
  .pulse {
    animation: pulse 2s infinite;
  }

  @keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
  }