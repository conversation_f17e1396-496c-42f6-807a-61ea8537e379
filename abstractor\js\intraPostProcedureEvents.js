import {
  formatHeading,
  updateTileStyle,
  validateStringInput,
  formatDisplayDate,
  formatDisplayDateTime,
} from "../utils.js";

function updateContainerHighlight(container, data) {
  // Check if all fields are filled (not used currently but kept for future use)
  Object.entries(data).forEach(([key, field]) => {
    if (key === "verified") return;
    if (key === "events") {
      // Handle the events structure
      if (field.elements) {
        Object.entries(field.elements).forEach(
          ([_category, categoryFields]) => {
            Object.entries(categoryFields).forEach(
              ([_eventKey, eventField]) => {
                if (
                  !eventField.value ||
                  eventField.value.toString().trim() === ""
                ) {
                  // Field is empty
                }
              }
            );
          }
        );
      }
    }
  });
  if (
    data.events &&
    data.events.verified &&
    data.events.verified.value === "True"
  ) {
    container.style.border = "2px solid green";
    container.style.borderRadius = "8px";
  } else {
    container.style.border = "2px solid red";
    container.style.borderRadius = "8px";
  }
}

// Function to clear values in conditional fields
function clearConditionalValues(field, conditionalKey) {
  if (!field[conditionalKey] || typeof field[conditionalKey] !== "object") {
    return;
  }

  // If it's a direct field with input_type (like a date field)
  if (field[conditionalKey].input_type) {
    field[conditionalKey].value = "";
    field[conditionalKey].modified_by = "";
  } else {
    // If it's an object with multiple sub-fields
    Object.keys(field[conditionalKey]).forEach((subKey) => {
      const subField = field[conditionalKey][subKey];
      if (subField && typeof subField === "object") {
        if (subField.input_type === "multi_select" && Array.isArray(subField.value)) {
          subField.value = [];
        } else {
          subField.value = "";
        }
        subField.modified_by = "";
      }
    });
  }
}

// Function to render conditional content based on selected option
// This function can be called recursively for nested multi_input_fields
function renderConditionalContent(
  selectedOption,
  parentField,
  parentContainer,
  parentKey,
  nestedPath = []
) {
  // Ensure parentContainer is provided
  if (!parentContainer) {
    console.error("parentContainer is required for renderConditionalContent");
    return;
  }

  // Clear previous content
  parentContainer.innerHTML = "";
  parentContainer.style.display = "none";

  // Determine which conditional content to show based on selected option
  let conditionalKey = null;
  if (selectedOption === "Yes" && parentField.if_yes) {
    conditionalKey = "if_yes";
  } else if (selectedOption === "No" && parentField.if_no) {
    conditionalKey = "if_no";
  } else if (selectedOption === "Alive" && parentField.if_alive) {
    conditionalKey = "if_alive";
  } else if (selectedOption === "Deceased" && parentField.if_deceased) {
    conditionalKey = "if_deceased";
  }

  // If no conditional content is available, return
  if (!conditionalKey || !parentField[conditionalKey]) {
    console.error(
      "No conditional content available for",
      selectedOption,
      parentField
    );
    return;
  }

  // Show the container
  parentContainer.style.display = "block";

  // For debugging


  // Check if the conditional content is valid
  if (typeof parentField[conditionalKey] !== "object") {
    console.error(
      "Invalid conditional content type:",
      typeof parentField[conditionalKey]
    );
    return;
  }

  // Render each sub-field in the conditional content
  // First check if the conditional content is a single field or an object with multiple fields
  if (
    parentField[conditionalKey] &&
    typeof parentField[conditionalKey] === "object"
  ) {
    // If it's a date field directly (common pattern in this app)
    if (parentField[conditionalKey].input_type === "date") {

      const subField = parentField[conditionalKey];
      // No need for subKey in this case

      // Create a container for the sub-field
      const subFieldContainer = document.createElement("div");
      subFieldContainer.classList.add("sub-field-container");
      subFieldContainer.style.marginBottom = "15px";

      // Create a label for the sub-field
      const subLabel = document.createElement("label");
      subLabel.classList.add("label");
      // Ensure we have valid values for label and field_id
      const labelText = subField.label || "Event Date";
      const fieldId = subField.field_id || "";
      subLabel.innerHTML = `${labelText} ${fieldId ? `(${fieldId})` : ""}`;
      if (subField.description) {
        subLabel.setAttribute("title", subField.description);
      }
      subFieldContainer.appendChild(subLabel);

      // Create date input
      const dateWrapper = document.createElement("div");
      dateWrapper.style.position = "relative";

      // Create display input
      const displayInput = document.createElement("input");
      displayInput.type = "text";
      displayInput.name = `${parentKey}-display`;
      displayInput.readOnly = true;

      // Ensure we have a valid date value
      const dateValue = subField.value || "";

      const formattedDate = formatDisplayDate(dateValue);

      displayInput.value = formattedDate;
      displayInput.placeholder = "MM/DD/YYYY";
      displayInput.style.cursor = "pointer";

      // Hidden date input
      const dateInput = document.createElement("input");
      dateInput.type = "date";
      dateInput.name = `${parentKey}`;
      dateInput.value = subField.value || "";
      dateInput.style.position = "absolute";
      dateInput.style.opacity = "0";
      dateInput.style.cursor = "pointer";

      // Set max date to today
      const today = new Date().toISOString().split("T")[0];
      dateInput.max = today;

      dateInput.addEventListener("change", (e) => {
        const selectedDate = e.target.value;

        displayInput.value = formatDisplayDate(selectedDate);

        // Update the data model
        if (parentField[conditionalKey]) {
          parentField[conditionalKey].value = selectedDate;
          parentField[conditionalKey].modified_by = "ABSTRACTOR";

          // Update the UI to show the modified status
          const modifiedByDisplay = document.querySelector(
            `#${parentKey}-modified-by`
          );
          if (modifiedByDisplay) {
            modifiedByDisplay.textContent = "ABSTRACTOR";
          }
        } else {
          console.error(
            "Cannot update date value, invalid data structure",
            parentField
          );
        }
      });

      // Trigger date picker when clicking display input
      displayInput.addEventListener("click", () => {
        dateInput.showPicker();
      });

      dateWrapper.appendChild(displayInput);
      dateWrapper.appendChild(dateInput);
      subFieldContainer.appendChild(dateWrapper);

      // Add the sub-field container to the parent container
      parentContainer.appendChild(subFieldContainer);
      return; // Exit early since we've handled the special case
    }

    // Regular case: iterate through the fields
    Object.entries(parentField[conditionalKey]).forEach(
      ([subKey, subField]) => {
        // Create a container for the sub-field
        const subFieldContainer = document.createElement("div");
        subFieldContainer.classList.add("sub-field-container");
        subFieldContainer.style.marginBottom = "15px";

        // Create a label for the sub-field
        const subLabel = document.createElement("label");
        subLabel.classList.add("label");
        subLabel.innerHTML = `${subField.label} ${
          subField.field_id ? `(${subField.field_id})` : ""
        }`;
        if (subField.description) {
          subLabel.setAttribute("title", subField.description);
        }
        subFieldContainer.appendChild(subLabel);

        // If a metric is available, insert it after the label
        if (subField.metric) {
          const metricLabel = document.createElement("span");
          metricLabel.classList.add("metric");
          metricLabel.textContent = ` (${subField.metric})`;
          subFieldContainer.appendChild(metricLabel);
        }

        // Process the sub-field based on its input type
        if (
          subField.input_type === "string" ||
          subField.input_type === "text"
        ) {
          const input = document.createElement("input");
          input.type = "text";
          input.name = `${parentKey}-${subKey}`;
          input.placeholder = `Enter ${subField.label}`;
          input.value = subField.value || "";

          let previousValue = input.value;

          input.addEventListener("input", (e) => {
            const currentValue = e.target.value;

            // Use validation utilities with temporary callback
            let isValid = false;
            validateStringInput(
              currentValue,
              subField.field_id,
              (validatedValue) => {
                isValid = true;
                previousValue = validatedValue;

                // Update model and UI only if value is valid
                parentField[conditionalKey][subKey].value = validatedValue;
                parentField[conditionalKey][subKey].modified_by = "ABSTRACTOR";

                // Update input value if validation modified it
                if (validatedValue !== currentValue) {
                  e.target.value = validatedValue;
                }
              }
            );

            // Revert to previous value if validation failed
            if (!isValid) {
              e.target.value = previousValue;
            }
          });

          subFieldContainer.appendChild(input);
        } else if (subField.input_type === "date") {

          // Create date input
          const dateWrapper = document.createElement("div");
          dateWrapper.style.position = "relative";

          // Create display input
          const displayInput = document.createElement("input");
          displayInput.type = "text";
          displayInput.name = `${parentKey}-${subKey}-display`;
          displayInput.readOnly = true;
          displayInput.value = formatDisplayDate(subField.value || "");
          displayInput.placeholder = "MM/DD/YYYY";
          displayInput.style.cursor = "pointer";

          // Hidden date input
          const dateInput = document.createElement("input");
          dateInput.type = "date";
          dateInput.name = `${parentKey}-${subKey}`;
          dateInput.value = subField.value || "";
          dateInput.style.position = "absolute";
          dateInput.style.opacity = "0";
          dateInput.style.cursor = "pointer";

          // Set max date to today
          const today = new Date().toISOString().split("T")[0];
          dateInput.max = today;

          dateInput.addEventListener("change", (e) => {
            const selectedDate = e.target.value;
            displayInput.value = formatDisplayDate(selectedDate);
            parentField[conditionalKey][subKey].value = selectedDate;
            parentField[conditionalKey][subKey].modified_by = "ABSTRACTOR";

            // Update the UI to show the modified status
            const modifiedByDisplay = document.querySelector(
              `#${parentKey}-modified-by`
            );
            if (modifiedByDisplay) {
              modifiedByDisplay.textContent = "ABSTRACTOR";
            }
          });

          // Trigger date picker when clicking display input
          displayInput.addEventListener("click", () => {
            dateInput.showPicker();
          });

          dateWrapper.appendChild(displayInput);
          dateWrapper.appendChild(dateInput);
          subFieldContainer.appendChild(dateWrapper);
        } else if (subField.input_type === "date_time") {
          const dateTimeWrapper = document.createElement("div");
          dateTimeWrapper.style.position = "relative";
          dateTimeWrapper.style.display = "flex";
          dateTimeWrapper.style.flexDirection = "column";
          dateTimeWrapper.style.gap = "8px";

          // Create display input for the combined date and time
          const displayInput = document.createElement("input");
          displayInput.type = "text";
          displayInput.name = `${parentKey}-${subKey}-display`;
          displayInput.readOnly = true;
          displayInput.value = formatDisplayDateTime(subField.value);
          displayInput.placeholder = "MM/DD/YYYY HH:MM AM/PM";
          displayInput.style.cursor = "pointer";

          // Create a container for the date and time pickers
          const pickersContainer = document.createElement("div");
          pickersContainer.style.display = "flex";
          pickersContainer.style.gap = "8px";
          pickersContainer.style.position = "absolute";
          pickersContainer.style.opacity = "0";
          pickersContainer.style.pointerEvents = "none";

          // Hidden date input
          const dateInput = document.createElement("input");
          dateInput.type = "date";
          dateInput.name = `${parentKey}-${subKey}-date`;
          dateInput.style.cursor = "pointer";

          // Hidden time input
          const timeInput = document.createElement("input");
          timeInput.type = "time";
          timeInput.name = `${parentKey}-${subKey}-time`;
          timeInput.style.cursor = "pointer";

          // Set initial values if available
          if (subField.value) {
            const dateObj = new Date(subField.value);
            if (!isNaN(dateObj.getTime())) {
              // Set date value (YYYY-MM-DD)
              dateInput.value = dateObj.toISOString().split("T")[0];

              // Set time value (HH:MM)
              const hours = String(dateObj.getHours()).padStart(2, "0");
              const minutes = String(dateObj.getMinutes()).padStart(2, "0");
              timeInput.value = `${hours}:${minutes}`;
            }
          }

          // Set max date to today
          const today = new Date().toISOString().split("T")[0];
          dateInput.max = today;

          // Function to update the combined date-time value
          const updateDateTime = () => {
            if (dateInput.value) {
              let dateTimeValue;

              if (timeInput.value) {
                // Combine date and time
                const [hours, minutes] = timeInput.value.split(":");
                const dateObj = new Date(dateInput.value);
                dateObj.setHours(parseInt(hours, 10));
                dateObj.setMinutes(parseInt(minutes, 10));
                dateTimeValue = dateObj.toISOString();
              } else {
                // Use just the date with time set to midnight
                dateTimeValue = `${dateInput.value}T00:00:00.000Z`;
              }

              // Update display and model
              displayInput.value = formatDisplayDateTime(dateTimeValue);
              parentField[conditionalKey][subKey].value = dateTimeValue;
              parentField[conditionalKey][subKey].modified_by = "ABSTRACTOR";
            }
          };

          // Event listeners for date and time inputs
          dateInput.addEventListener("change", updateDateTime);
          timeInput.addEventListener("change", updateDateTime);

          // Show date picker when clicking the display input
          displayInput.addEventListener("click", () => {
            // Make the pickers container visible temporarily
            pickersContainer.style.opacity = "1";
            pickersContainer.style.pointerEvents = "auto";
            pickersContainer.style.zIndex = "100";

            // Show the date picker first
            dateInput.showPicker();

            // After date is selected, show time picker
            dateInput.addEventListener(
              "change",
              function onDateSelected() {
                setTimeout(() => {
                  timeInput.showPicker();
                }, 100);
                dateInput.removeEventListener("change", onDateSelected);
              },
              { once: true }
            );

            // Hide the pickers container after time is selected
            timeInput.addEventListener(
              "change",
              function onTimeSelected() {
                setTimeout(() => {
                  pickersContainer.style.opacity = "0";
                  pickersContainer.style.pointerEvents = "none";
                }, 500);
                timeInput.removeEventListener("change", onTimeSelected);
              },
              { once: true }
            );
          });

          // Add inputs to their containers
          pickersContainer.appendChild(dateInput);
          pickersContainer.appendChild(timeInput);
          dateTimeWrapper.appendChild(displayInput);
          dateTimeWrapper.appendChild(pickersContainer);
          subFieldContainer.appendChild(dateTimeWrapper);
        } else if (subField.input_type === "radio") {
          // Create radio buttons container
          const subRadioContainer = document.createElement("div");
          subRadioContainer.classList.add("radio-container");
          subRadioContainer.style.display = "flex";
          subRadioContainer.style.flexDirection = "row";
          subRadioContainer.style.flexWrap = "nowrap"; // Prevent wrapping to keep in one line
          subRadioContainer.style.gap = "15px";

          subField.options.forEach((option) => {
            const radioWrapper = document.createElement("div");
            radioWrapper.style.display = "flex";
            radioWrapper.style.alignItems = "center";

            const input = document.createElement("input");
            input.type = "radio";
            input.name = `${parentKey}-${subKey}`;
            input.value = option;
            input.id = `${parentKey}-${subKey}-${option}`;
            input.checked = subField.value === option;

            input.addEventListener("change", (e) => {
              if (e.target.checked) {
                // Update the sub-field value
                parentField[conditionalKey][subKey].value = e.target.value;
                parentField[conditionalKey][subKey].modified_by = "ABSTRACTOR";
              }
            });

            const optionLabel = document.createElement("label");
            optionLabel.setAttribute("for", `${parentKey}-${subKey}-${option}`);
            optionLabel.innerText = option;
            if (subField.description) {
              optionLabel.setAttribute("title", subField.description);
            }

            radioWrapper.appendChild(input);
            radioWrapper.appendChild(optionLabel);
            subRadioContainer.appendChild(radioWrapper);
          });

          subFieldContainer.appendChild(subRadioContainer);
        } else if (subField.input_type === "select") {
          // Create select dropdown
          const select = document.createElement("select");
          select.name = `${parentKey}-${subKey}`;

          // Add a default option if no value is set
          if (!subField.value) {
            const defaultOption = document.createElement("option");
            defaultOption.value = "";
            defaultOption.innerText = "Select an option";
            defaultOption.disabled = true;
            defaultOption.selected = true;
            if (subField.description) {
              defaultOption.setAttribute("title", subField.description);
            }
            select.appendChild(defaultOption);
          }

          // Add options to the select dropdown
          subField.options.forEach((option) => {
            const optionElement = document.createElement("option");
            optionElement.value = option.id;
            optionElement.innerText = option.value;
            if (subField.value && subField.value === option.value) {
              optionElement.selected = true;
            }
            select.appendChild(optionElement);
          });

          select.addEventListener("change", (e) => {
            const selOption = subField.options.find(
              (option) => option.id === e.target.value
            );
            const newValue = selOption ? selOption.value : "";
            parentField[conditionalKey][subKey].value = newValue;
            parentField[conditionalKey][subKey].modified_by = "ABSTRACTOR";
          });

          subFieldContainer.appendChild(select);
        } else if (subField.input_type === "multi_input_field") {
          // Handle nested multi_input_field
          const nestedMultiInputContainer = document.createElement("div");
          nestedMultiInputContainer.classList.add("multi-input-container");
          nestedMultiInputContainer.style.marginTop = "10px";

          // Create radio buttons for the nested multi_input_field
          const nestedRadioContainer = document.createElement("div");
          nestedRadioContainer.classList.add("radio-container");
          nestedRadioContainer.style.display = "flex";
          nestedRadioContainer.style.flexDirection = "row"; // Display options horizontally in the same line
          nestedRadioContainer.style.flexWrap = "nowrap"; // Prevent wrapping to keep in one line
          nestedRadioContainer.style.gap = "15px";
          nestedRadioContainer.style.width = "100%";

          // Function to update the nested field value
          const updateNestedValue = (value) => {
            parentField[conditionalKey][subKey].value = value;
            parentField[conditionalKey][subKey].modified_by = "ABSTRACTOR";
          };

          // Keep track of all conditional containers to hide them when needed
          const allNestedConditionalContainers = [];

          // Create radio buttons for each option in the nested multi_input_field
          subField.options.forEach((option) => {
            // Create a wrapper for each radio option and its conditional content
            const optionContainer = document.createElement("div");
            optionContainer.classList.add("option-container");
            optionContainer.style.marginRight = "20px"; // Add margin to the right instead of bottom
            optionContainer.style.display = "inline-block"; // Use inline-block for better layout
            optionContainer.style.verticalAlign = "top"; // Align to the top
            optionContainer.style.width = option === "Yes" ? "100%" : "auto"; // Make Yes option container full width

            // Create the radio option row
            const radioWrapper = document.createElement("div");
            radioWrapper.style.display = "flex";
            radioWrapper.style.alignItems = "center";
            radioWrapper.style.marginBottom = "5px";

            const input = document.createElement("input");
            input.type = "radio";
            input.name = `${parentKey}-${subKey}`;
            input.value = option;
            input.id = `${parentKey}-${subKey}-${option}`;
            input.checked = subField.value === option;

            const optionLabel = document.createElement("label");
            optionLabel.setAttribute("for", `${parentKey}-${subKey}-${option}`);
            optionLabel.innerText = option;
            if (subField.description) {
              optionLabel.setAttribute("title", subField.description);
            }

            radioWrapper.appendChild(input);
            radioWrapper.appendChild(optionLabel);
            optionContainer.appendChild(radioWrapper);

            // Create a container for this option's conditional content
            const optionConditionalContainer = document.createElement("div");
            optionConditionalContainer.classList.add(
              "conditional-content-container"
            );
            // Position the conditional content directly below the radio options in the same tile
            optionConditionalContainer.style.marginTop = "10px";
            optionConditionalContainer.style.width = "100%";
            optionConditionalContainer.style.padding = "5px"; // Reduced padding for more compact layout
            optionConditionalContainer.style.border = "1px solid #eee";
            optionConditionalContainer.style.borderRadius = "4px";
            optionConditionalContainer.style.display = "none"; // Initially hidden
            optionContainer.appendChild(optionConditionalContainer);

            // Add to tracking array
            allNestedConditionalContainers.push(optionConditionalContainer);

            // Update data model and render nested conditional content
            input.addEventListener("change", (e) => {
              if (e.target.checked) {
                // Update the nested field value
                updateNestedValue(e.target.value);

                // Clear values from other conditional sections when switching options
                if (e.target.value === "No" && subField.if_yes) {
                  clearConditionalValues(subField, "if_yes");
                } else if (e.target.value === "Yes" && subField.if_no) {
                  clearConditionalValues(subField, "if_no");
                } else if (e.target.value === "Deceased" && subField.if_alive) {
                  clearConditionalValues(subField, "if_alive");
                } else if (e.target.value === "Alive" && subField.if_deceased) {
                  clearConditionalValues(subField, "if_deceased");
                }

                // Hide all conditional containers first
                allNestedConditionalContainers.forEach((container) => {
                  container.innerHTML = "";
                  container.style.display = "none";
                });

                // Determine which nested conditional content to show
                let nestedConditionalKey = null;
                if (e.target.value === "Yes" && subField.if_yes) {
                  nestedConditionalKey = "if_yes";
                } else if (e.target.value === "No" && subField.if_no) {
                  nestedConditionalKey = "if_no";
                } else if (e.target.value === "Alive" && subField.if_alive) {
                  nestedConditionalKey = "if_alive";
                } else if (
                  e.target.value === "Deceased" &&
                  subField.if_deceased
                ) {
                  nestedConditionalKey = "if_deceased";
                }

                // Render nested conditional content if available
                if (nestedConditionalKey && subField[nestedConditionalKey]) {
                  // Create a new path for this nested level
                  const newPath = [
                    ...nestedPath,
                    { key: subKey, conditionalKey },
                  ];
                  // Recursively render the nested conditional content
                  renderConditionalContent(
                    option,
                    subField,
                    optionConditionalContainer,
                    `${parentKey}-${subKey}`,
                    newPath
                  );
                }
              }
            });

            // Add the option container to the radio container
            nestedRadioContainer.appendChild(optionContainer);

            // Render initial conditional content if this option is selected
            if (subField.value === option) {
              // Determine which nested conditional content to show
              let nestedConditionalKey = null;
              if (option === "Yes" && subField.if_yes) {
                nestedConditionalKey = "if_yes";

              } else if (option === "No" && subField.if_no) {
                nestedConditionalKey = "if_no";
              } else if (option === "Alive" && subField.if_alive) {
                nestedConditionalKey = "if_alive";
              } else if (option === "Deceased" && subField.if_deceased) {
                nestedConditionalKey = "if_deceased";
              }

              // Render nested conditional content if available
              if (nestedConditionalKey && subField[nestedConditionalKey]) {
                renderConditionalContent(
                  option,
                  subField,
                  optionConditionalContainer,
                  `${parentKey}-${subKey}`,
                  [...nestedPath, { key: subKey, conditionalKey }]
                );
              }
            }
          });

          // Add radio container to the multi-input container
          nestedMultiInputContainer.appendChild(nestedRadioContainer);
          subFieldContainer.appendChild(nestedMultiInputContainer);
        } else if (subField.input_type === "multi_select") {
          // Initialize the value as an array if it's not already
          if (!Array.isArray(subField.value)) {
            subField.value = subField.value ? [subField.value] : [];
          }

          // Create dropdown container with relative positioning
          const dropdownContainer = document.createElement("div");
          dropdownContainer.classList.add("dropdown-container");
          dropdownContainer.style.position = "relative";
          dropdownContainer.style.width = "100%";

          // Create dropdown header/button
          const dropdownHeader = document.createElement("div");
          dropdownHeader.classList.add("dropdown-header");
          dropdownHeader.style.padding = "8px 12px";
          dropdownHeader.style.border = "1px solid #ccc";
          dropdownHeader.style.borderRadius = "4px";
          dropdownHeader.style.cursor = "pointer";
          dropdownHeader.style.display = "flex";
          dropdownHeader.style.justifyContent = "space-between";
          dropdownHeader.style.alignItems = "center";
          dropdownHeader.style.backgroundColor = "#fff";

          // Display selected values or placeholder
          const selectedText = document.createElement("span");
          selectedText.classList.add("selected-text");

          // Function to update the selected text display
          const updateSelectedText = () => {
            if (subField.value.length === 0) {
              selectedText.textContent = "Select options...";
            } else if (subField.value.length === 1) {
              const selectedOption = subField.options.find(
                (opt) => opt.value === subField.value[0]
              );
              selectedText.textContent = selectedOption
                ? selectedOption.value
                : subField.value[0];
            } else {
              selectedText.textContent = `${subField.value.length} options selected`;
            }
          };

          updateSelectedText();

          // Add dropdown arrow
          const dropdownArrow = document.createElement("span");
          dropdownArrow.innerHTML = "&#9662;"; // Down arrow character

          dropdownHeader.appendChild(selectedText);
          dropdownHeader.appendChild(dropdownArrow);

          // Create dropdown content (initially hidden)
          const dropdownContent = document.createElement("div");
          dropdownContent.classList.add("dropdown-content");
          dropdownContent.style.display = "none";
          dropdownContent.style.position = "absolute";
          dropdownContent.style.top = "100%";
          dropdownContent.style.left = "0";
          dropdownContent.style.width = "100%";
          dropdownContent.style.maxHeight = "200px";
          dropdownContent.style.overflowY = "auto";
          dropdownContent.style.backgroundColor = "#fff";
          dropdownContent.style.border = "1px solid #ccc";
          dropdownContent.style.borderRadius = "4px";
          dropdownContent.style.boxShadow = "0 4px 8px rgba(0,0,0,0.1)";
          dropdownContent.style.zIndex = "1000";

          // Create a checkbox for each option
          subField.options.forEach((option) => {
            const checkboxWrapper = document.createElement("div");
            checkboxWrapper.classList.add("checkbox-wrapper");
            checkboxWrapper.style.display = "flex";
            checkboxWrapper.style.alignItems = "center";
            checkboxWrapper.style.padding = "8px 12px";
            checkboxWrapper.style.borderBottom = "1px solid #eee";

            const input = document.createElement("input");
            input.type = "checkbox";
            input.name = `${parentKey}-${subKey}-${option.id}`;
            input.value = option.id;
            input.id = `${parentKey}-${subKey}-${option.id}`;
            input.style.marginRight = "8px";

            // Check if this option is in the selected values array
            input.checked = subField.value.includes(option.value);

            input.addEventListener("change", (e) => {
              // Get the current values array
              let currentValues = Array.isArray(subField.value)
                ? [...subField.value]
                : [];

              if (e.target.checked) {
                // Add the value if it's not already in the array
                if (!currentValues.includes(option.value)) {
                  currentValues.push(option.value);
                }
              } else {
                // Remove the value if it's in the array
                currentValues = currentValues.filter(
                  (val) => val !== option.value
                );
              }

              // Update the model
              parentField[conditionalKey][subKey].value = currentValues;
              parentField[conditionalKey][subKey].modified_by = "ABSTRACTOR";

              // Update selected text display
              updateSelectedText();
            });

            const optionLabel = document.createElement("label");
            optionLabel.setAttribute(
              "for",
              `${parentKey}-${subKey}-${option.id}`
            );
            optionLabel.innerText = option.value;

            checkboxWrapper.appendChild(input);
            checkboxWrapper.appendChild(optionLabel);
            dropdownContent.appendChild(checkboxWrapper);
          });

          // Toggle dropdown on click
          dropdownHeader.addEventListener("click", () => {
            const isOpen = dropdownContent.style.display === "block";
            dropdownContent.style.display = isOpen ? "none" : "block";
            dropdownArrow.innerHTML = isOpen ? "&#9662;" : "&#9652;";
          });

          // Close dropdown when clicking outside
          document.addEventListener("click", (e) => {
            if (!dropdownContainer.contains(e.target)) {
              dropdownContent.style.display = "none";
              dropdownArrow.innerHTML = "&#9662;";
            }
          });

          dropdownContainer.appendChild(dropdownHeader);
          dropdownContainer.appendChild(dropdownContent);
          subFieldContainer.appendChild(dropdownContainer);
        }

        // Add the sub-field container to the parent container
        parentContainer.appendChild(subFieldContainer);
      }
    );
  } else {
    console.error(
      "Invalid conditional content structure:",
      parentField[conditionalKey]
    );
  }
}

export function renderIntraPostProcedureEvents(patientData) {
  const container = document.getElementById("intraPostProcedureEvents");
  container.innerHTML = "";
  container.style.position = "relative";

  // Get the events object which contains all the categories
  const eventsData = patientData.intra_or_post_procedure_events.events;

  if (!eventsData || !eventsData.elements) {
    console.error("Events data or elements not found");
    return;
  }

  // Render each category of events
  Object.entries(eventsData.elements).forEach(([category, fields]) => {
    if (!fields || Object.keys(fields).length === 0) return;

    const categoryContainer = document.createElement("div");
    categoryContainer.classList.add("category-container");
    categoryContainer.style.display = "contents";

    const categoryLabel = document.createElement("h4");
    categoryLabel.style.fontWeight = "bold";
    categoryLabel.style.gridColumn = "1 / -1";
    categoryLabel.innerText = formatHeading(category);
    categoryContainer.appendChild(categoryLabel);

    Object.entries(fields).forEach(([key, value]) => {
      const tileWrapper = document.createElement("div");
      tileWrapper.classList.add("tile-wrapper");
      tileWrapper.style.position = "relative";
      tileWrapper.style.marginBottom = "24px";

      const fieldContainer = document.createElement("div");
      fieldContainer.classList.add("field-container");

      const label = document.createElement("label");
      label.classList.add("label", "cursor-pointer");
      label.innerHTML = `${value.label} (${value.field_id})`;
      if (value.description) {
        label.setAttribute("title", value.description);
      }
      fieldContainer.appendChild(label);

      if (value.metric) {
        const metricLabel = document.createElement("span");
        metricLabel.classList.add("metric");
        metricLabel.textContent = ` (${value.metric})`;
        fieldContainer.appendChild(metricLabel);
      }

      if (value.input_type === "string" || value.input_type === "text") {
        const input = document.createElement("input");
        input.type = "text";
        input.name = key;
        input.placeholder = `Enter ${value.label}`;
        input.value = value.value || "";

        updateTileStyle(fieldContainer, input.value);

        let previousValue = input.value;

        input.addEventListener("input", (e) => {
          const currentValue = e.target.value;

          // Use our existing validation utilities with a temporary callback
          // that tells us if the value was accepted
          let isValid = false;
          validateStringInput(
            currentValue,
            value.field_id,
            (validatedValue) => {
              isValid = true;
              previousValue = validatedValue;

              // Update the model and UI only if the value is valid
              value.value = validatedValue;
              value.modified_by = "ABSTRACTOR";
              updateTileStyle(fieldContainer, validatedValue);
              modifiedByDisplay.textContent = "ABSTRACTOR";
              updateContainerHighlight(
                container,
                patientData.intra_or_post_procedure_events
              );

              // Update the input value if the validation modified it
              if (validatedValue !== currentValue) {
                e.target.value = validatedValue;
              }
            }
          );

          // If the validation didn't accept the value, revert to previous valid value
          if (!isValid) {
            e.target.value = previousValue;
          }
        });

        fieldContainer.appendChild(input);
      } else if (value.input_type === "date") {
        const dateWrapper = document.createElement("div");
        dateWrapper.style.position = "relative";

        // Create display input
        const displayInput = document.createElement("input");
        displayInput.type = "text";
        displayInput.name = `${key}_display`;
        displayInput.readOnly = true;
        displayInput.value = formatDisplayDate(value.value);
        displayInput.placeholder = "MM/DD/YYYY";
        displayInput.style.cursor = "pointer";

        // Hidden date input
        const dateInput = document.createElement("input");
        dateInput.type = "date";
        dateInput.name = key;
        dateInput.value = value.value || "";
        dateInput.style.position = "absolute";
        dateInput.style.opacity = "0";
        dateInput.style.cursor = "pointer";

        // Set max date to today
        const today = new Date().toISOString().split("T")[0];
        dateInput.max = today;

        updateTileStyle(fieldContainer, dateInput.value);

        dateInput.addEventListener("change", (e) => {
          const selectedDate = e.target.value;
          displayInput.value = formatDisplayDate(selectedDate);
          value.value = selectedDate;
          value.modified_by = "ABSTRACTOR";
          updateTileStyle(fieldContainer, selectedDate);
          modifiedByDisplay.textContent = "ABSTRACTOR";
          updateContainerHighlight(
            container,
            patientData.intra_or_post_procedure_events
          );
        });

        // Trigger date picker when clicking display input
        displayInput.addEventListener("click", () => {
          dateInput.showPicker();
        });

        dateWrapper.appendChild(displayInput);
        dateWrapper.appendChild(dateInput);
        fieldContainer.appendChild(dateWrapper);
      } else if (value.input_type === "radio") {
        const radioContainer = document.createElement("div");
        radioContainer.classList.add("radio-container");
        value.options.forEach((option) => {
          const radioWrapper = document.createElement("div");
          const radioInput = document.createElement("input");
          radioInput.type = "radio";
          radioInput.name = key;
          radioInput.value = option;
          radioInput.id = `${key}-${option}`;
          radioInput.checked = value.value === option;
          radioInput.addEventListener("change", (event) => {
            if (event.target.checked) {
              value.value = event.target.value;
              value.modified_by = "ABSTRACTOR";
              updateTileStyle(fieldContainer, event.target.value);
              modifiedByDisplay.textContent = "ABSTRACTOR";
              updateContainerHighlight(
                container,
                patientData.intra_or_post_procedure_events
              );
            }
          });
          const optionLabel = document.createElement("label");
          optionLabel.classList.add("label", "cursor-pointer");
          optionLabel.setAttribute("for", `${key}-${option}`);
          optionLabel.innerText = option;
          if (value.description) {
            optionLabel.setAttribute("title", value.description);
          }
          radioWrapper.appendChild(radioInput);
          radioWrapper.appendChild(optionLabel);
          radioContainer.appendChild(radioWrapper);
        });
        updateTileStyle(fieldContainer, value.value || "");
        fieldContainer.appendChild(radioContainer);
      } else if (value.input_type === "select") {
        const select = document.createElement("select");
        select.name = key;
        if (!value.value) {
          const defaultOption = document.createElement("option");
          defaultOption.value = "";
          defaultOption.innerText = "Select an option";
          defaultOption.disabled = true;
          defaultOption.selected = true;
          select.appendChild(defaultOption);
        }
        value.options.forEach((option) => {
          const optionElement = document.createElement("option");
          optionElement.value = option.id;
          optionElement.innerText = option.value;
          if (value.value && value.value === option.value) {
            optionElement.selected = true;
          }
          select.appendChild(optionElement);
        });
        select.addEventListener("change", (event) => {
          const selectedOption = value.options.find(
            (option) => option.id === event.target.value
          );
          value.value = selectedOption ? selectedOption.value : "";
          value.modified_by = "ABSTRACTOR";
          updateTileStyle(fieldContainer, value.value);
          modifiedByDisplay.textContent = "ABSTRACTOR";
          updateContainerHighlight(
            container,
            patientData.intra_or_post_procedure_events
          );
        });
        const initOption = value.options.find(
          (option) => option.value === value.value
        );
        updateTileStyle(fieldContainer, initOption ? initOption.value : "");
        fieldContainer.appendChild(select);
      } else if (value.input_type === "multi_select") {
        // Initialize the value as an array if it's not already
        if (!Array.isArray(value.value)) {
          value.value = value.value ? [value.value] : [];
        }

        // Create dropdown container with relative positioning
        const dropdownContainer = document.createElement("div");
        dropdownContainer.classList.add("dropdown-container");
        dropdownContainer.style.position = "relative";
        dropdownContainer.style.width = "100%";

        // Make sure all parent containers allow overflow
        fieldContainer.style.overflow = "visible";
        const tileWrapper = fieldContainer.parentElement;
        if (tileWrapper && tileWrapper.classList.contains("tile-wrapper")) {
          tileWrapper.style.overflow = "visible";
        }
        // Also set the container's overflow to visible
        container.style.overflow = "visible";

        // Create dropdown header/button
        const dropdownHeader = document.createElement("div");
        dropdownHeader.classList.add("dropdown-header");
        dropdownHeader.style.padding = "8px 12px";
        dropdownHeader.style.border = "1px solid #ccc";
        dropdownHeader.style.borderRadius = "4px";
        dropdownHeader.style.cursor = "pointer";
        dropdownHeader.style.display = "flex";
        dropdownHeader.style.justifyContent = "space-between";
        dropdownHeader.style.alignItems = "center";
        dropdownHeader.style.backgroundColor = "#fff";

        // Display selected values or placeholder
        const selectedText = document.createElement("span");
        selectedText.classList.add("selected-text");

        // Function to update the selected text display
        const updateSelectedText = () => {
          if (value.value.length === 0) {
            selectedText.textContent = "Select options...";
          } else if (value.value.length === 1) {
            const selectedOption = value.options.find(
              (opt) => opt.value === value.value[0]
            );
            selectedText.textContent = selectedOption
              ? selectedOption.value
              : value.value[0];
          } else {
            selectedText.textContent = `${value.value.length} options selected`;
          }
        };

        updateSelectedText();

        // Add dropdown arrow
        const dropdownArrow = document.createElement("span");
        dropdownArrow.innerHTML = "&#9662;"; // Down arrow character

        dropdownHeader.appendChild(selectedText);
        dropdownHeader.appendChild(dropdownArrow);

        // Create dropdown content (initially hidden)
        const dropdownContent = document.createElement("div");
        dropdownContent.classList.add("dropdown-content");
        dropdownContent.style.display = "none";
        // Position the dropdown directly under the header
        dropdownContent.style.position = "fixed"; // Use fixed positioning to ensure visibility
        dropdownContent.style.width = "350px"; // Fixed width to ensure content is visible
        dropdownContent.style.maxHeight = "200px";
        dropdownContent.style.overflowY = "auto";
        dropdownContent.style.overflowX = "hidden";
        dropdownContent.style.backgroundColor = "#fff";
        dropdownContent.style.border = "1px solid #ccc";
        dropdownContent.style.borderRadius = "4px";
        dropdownContent.style.boxShadow = "0 4px 8px rgba(0,0,0,0.3)";
        dropdownContent.style.zIndex = "9999"; // Very high z-index to ensure it's on top

        // Create a checkbox for each option
        value.options.forEach((option) => {
          const checkboxWrapper = document.createElement("div");
          checkboxWrapper.classList.add("checkbox-wrapper");
          checkboxWrapper.style.display = "flex";
          checkboxWrapper.style.alignItems = "center";
          checkboxWrapper.style.padding = "8px 12px";
          checkboxWrapper.style.borderBottom = "1px solid #eee";
          checkboxWrapper.style.textAlign = "left";
          checkboxWrapper.style.cursor = "pointer";

          const input = document.createElement("input");
          input.type = "checkbox";
          input.name = `${key}-${option.id}`;
          input.value = option.id;
          input.id = `${key}-${option.id}`;
          input.style.marginRight = "4px"; // Add a small space between checkbox and text

          // Check if this option is in the selected values array
          input.checked = value.value.includes(option.value);

          input.addEventListener("change", (e) => {
            // Get the current values array
            let currentValues = Array.isArray(value.value)
              ? [...value.value]
              : [];

            if (e.target.checked) {
              // Add the value if it's not already in the array
              if (!currentValues.includes(option.value)) {
                currentValues.push(option.value);
              }
            } else {
              // Remove the value if it's in the array
              currentValues = currentValues.filter(
                (val) => val !== option.value
              );
            }

            // Update the model
            value.value = currentValues;
            value.modified_by = "ABSTRACTOR";

            // Update selected text display
            updateSelectedText();

            // Update UI
            updateTileStyle(
              fieldContainer,
              currentValues.length > 0 ? "filled" : ""
            );
            modifiedByDisplay.textContent = "ABSTRACTOR";
            updateContainerHighlight(
              container,
              patientData.intra_or_post_procedure_events
            );
          });

          const optionLabel = document.createElement("label");
          optionLabel.setAttribute("for", `${key}-${option.id}`);
          optionLabel.style.marginLeft = "0";
          optionLabel.style.display = "inline-block";
          optionLabel.style.whiteSpace = "nowrap";
          optionLabel.style.cursor = "pointer";
          optionLabel.style.flexGrow = "1";

          // Display field_id in parentheses if available
          if (option.field_id) {
            optionLabel.innerText = `${option.value} (${option.field_id})`;
          } else {
            optionLabel.innerText = option.value;
          }

          // Create a wrapper for the checkbox and label to ensure they're tightly aligned
          const inputLabelWrapper = document.createElement("div");
          inputLabelWrapper.style.display = "flex";
          inputLabelWrapper.style.alignItems = "center";
          inputLabelWrapper.style.gap = "0";

          inputLabelWrapper.appendChild(input);
          inputLabelWrapper.appendChild(optionLabel);
          checkboxWrapper.appendChild(inputLabelWrapper);
          dropdownContent.appendChild(checkboxWrapper);
        });

        // Function to position the dropdown content properly
        const positionDropdown = () => {
          const headerRect = dropdownHeader.getBoundingClientRect();
          const viewportHeight = window.innerHeight;
          const viewportWidth = window.innerWidth;

          // Position dropdown below the header
          dropdownContent.style.top = `${headerRect.bottom}px`;

          // Ensure dropdown doesn't go off the right edge of the screen
          const rightEdge = headerRect.left + 250; // 250px is our dropdown width
          if (rightEdge > viewportWidth) {
            // Align to the right edge of the header instead
            dropdownContent.style.left = `${headerRect.right - 250}px`;
          } else {
            dropdownContent.style.left = `${headerRect.left}px`;
          }

          // Set max height based on available space
          const spaceBelow = viewportHeight - headerRect.bottom;
          const maxHeight = Math.max(100, Math.min(200, spaceBelow - 20));
          dropdownContent.style.maxHeight = `${maxHeight}px`;
        };

        // Close dropdown when clicking outside
        document.addEventListener("click", (e) => {
          if (
            !dropdownContainer.contains(e.target) &&
            !dropdownContent.contains(e.target)
          ) {
            dropdownContent.style.display = "none";
            dropdownArrow.innerHTML = "&#9662;";
          }
        });

        // Simple function to close the dropdown
        const closeDropdown = () => {
          dropdownContent.style.display = "none";
          dropdownArrow.innerHTML = "&#9662;";
          window.removeEventListener("scroll", closeDropdown);
        };

        // Clean up event listener when the component is removed
        const cleanupFunc = () => {
          if (document.body.contains(dropdownContent)) {
            document.body.removeChild(dropdownContent);
          }
          // Remove scroll event listener
          window.removeEventListener("scroll", closeDropdown);
        };

        // Store the cleanup function for potential future use
        dropdownContainer.cleanupFunc = cleanupFunc;

        // Add header to container, but add content to document body for better visibility
        dropdownContainer.appendChild(dropdownHeader);

        // Add the dropdown content to the document body when needed
        const showDropdown = () => {
          if (!document.body.contains(dropdownContent)) {
            document.body.appendChild(dropdownContent);
          }
          dropdownContent.style.display = "block";
          positionDropdown();
        };

        // Set up the click handler for the dropdown
        dropdownHeader.onclick = (e) => {
          e.stopPropagation();
          const isOpen = dropdownContent.style.display === "block";

          if (!isOpen) {
            showDropdown();
            dropdownArrow.innerHTML = "&#9652;"; // Up arrow
            window.addEventListener("scroll", closeDropdown);
          } else {
            closeDropdown();
          }
        };

        // Update the tile style based on whether any options are selected
        updateTileStyle(fieldContainer, value.value.length > 0 ? "filled" : "");

        fieldContainer.appendChild(dropdownContainer);
      } else if (value.input_type === "multi_input_field") {
        const multiInputContainer = document.createElement("div");
        multiInputContainer.classList.add("multi-input-container");
        multiInputContainer.style.position = "relative";

        // Create radio buttons for options instead of text input and checkbox
        const radioContainer = document.createElement("div");
        radioContainer.classList.add("radio-container");
        radioContainer.style.display = "flex";
        radioContainer.style.flexDirection = "row"; // Display options horizontally in the same line
        radioContainer.style.flexWrap = "nowrap"; // Prevent wrapping to keep in one line
        radioContainer.style.gap = "15px";
        radioContainer.style.width = "100%";

        // Keep track of all conditional containers to hide them when needed
        const allConditionalContainers = [];

        // Create radio buttons for each option
        value.options.forEach((option) => {
          // Create a wrapper for each radio option and its conditional content
          const optionContainer = document.createElement("div");
          optionContainer.classList.add("option-container");
          optionContainer.style.marginRight = "20px"; // Add margin to the right instead of bottom
          optionContainer.style.display = "inline-block"; // Use inline-block for better layout
          optionContainer.style.verticalAlign = "top"; // Align to the top
          optionContainer.style.width = option === "Yes" ? "100%" : "auto"; // Make Yes option container full width

          // Create the radio option row
          const radioWrapper = document.createElement("div");
          radioWrapper.style.display = "flex";
          radioWrapper.style.alignItems = "center";
          radioWrapper.style.marginBottom = "5px";

          const input = document.createElement("input");
          input.type = "radio";
          input.name = key;
          input.value = option;
          input.id = `${key}-${option}`;
          input.checked = value.value === option;

          const optionLabel = document.createElement("label");
          optionLabel.setAttribute("for", `${key}-${option}`);
          optionLabel.innerText = option;
          if (value.description) {
            optionLabel.setAttribute("title", value.description);
          }

          radioWrapper.appendChild(input);
          radioWrapper.appendChild(optionLabel);
          optionContainer.appendChild(radioWrapper);

          // Create a container for this option's conditional content
          const optionConditionalContainer = document.createElement("div");
          optionConditionalContainer.classList.add(
            "conditional-content-container"
          );
          // Position the conditional content directly below the radio options in the same tile
          optionConditionalContainer.style.marginTop = "10px";
          optionConditionalContainer.style.width = "100%";
          optionConditionalContainer.style.padding = "5px"; // Reduced padding for more compact layout
          optionConditionalContainer.style.border = "1px solid #eee";
          optionConditionalContainer.style.borderRadius = "4px";
          optionConditionalContainer.style.display = "none"; // Initially hidden
          optionContainer.appendChild(optionConditionalContainer);

          // Add to tracking array
          allConditionalContainers.push(optionConditionalContainer);

          // Update patientData and render conditional content when a radio is selected
          input.addEventListener("change", (e) => {
            if (e.target.checked) {
              value.value = e.target.value;
              value.modified_by = "ABSTRACTOR";
              updateTileStyle(fieldContainer, e.target.value);
              modifiedByDisplay.textContent = "ABSTRACTOR";
              updateContainerHighlight(
                container,
                patientData.intra_or_post_procedure_events
              );

              // Clear values from other conditional sections when switching options
              if (e.target.value === "No" && value.if_yes) {
                clearConditionalValues(value, "if_yes");
              } else if (e.target.value === "Yes" && value.if_no) {
                clearConditionalValues(value, "if_no");
              } else if (e.target.value === "Deceased" && value.if_alive) {
                clearConditionalValues(value, "if_alive");
              } else if (e.target.value === "Alive" && value.if_deceased) {
                clearConditionalValues(value, "if_deceased");
              }

              // Hide all conditional containers first
              allConditionalContainers.forEach((container) => {
                container.innerHTML = "";
                container.style.display = "none";
              });

              // Determine which conditional content to show based on selected option
              let conditionalKey = null;
              if (e.target.value === "Yes" && value.if_yes) {
                conditionalKey = "if_yes";
              } else if (e.target.value === "No" && value.if_no) {
                conditionalKey = "if_no";
              } else if (e.target.value === "Alive" && value.if_alive) {
                conditionalKey = "if_alive";
              } else if (e.target.value === "Deceased" && value.if_deceased) {
                conditionalKey = "if_deceased";
              }

              // Render conditional content if available
              if (conditionalKey && value[conditionalKey]) {
                renderConditionalContent(
                  option,
                  value,
                  optionConditionalContainer,
                  key
                );
              }
            }
          });

          // Add the complete option container to the radio container
          radioContainer.appendChild(optionContainer);

          // Render initial conditional content if this option is selected
          if (value.value === option) {
            // Determine which conditional content to show based on selected option
            let conditionalKey = null;
            if (option === "Yes" && value.if_yes) {
              conditionalKey = "if_yes";

            } else if (option === "No" && value.if_no) {
              conditionalKey = "if_no";
            } else if (option === "Alive" && value.if_alive) {
              conditionalKey = "if_alive";
            } else if (option === "Deceased" && value.if_deceased) {
              conditionalKey = "if_deceased";
            }

            // Render conditional content if available
            if (conditionalKey && value[conditionalKey]) {
              renderConditionalContent(
                option,
                value,
                optionConditionalContainer,
                key
              );
            }
          }
        });

        // Add radio container to the multi-input container
        multiInputContainer.appendChild(radioContainer);
        fieldContainer.appendChild(multiInputContainer);

        // Apply initial border styling using the selected radio value
        updateTileStyle(fieldContainer, value.value || "");
      }

      const modifiedByDisplay = document.createElement("span");
      modifiedByDisplay.style.display = "block";
      modifiedByDisplay.style.textAlign = "right";
      modifiedByDisplay.style.marginTop = "-10px";
      modifiedByDisplay.style.color = "#8143d9";
      modifiedByDisplay.style.fontSize = "12px";
      if (!value.modified_by) {
        value.modified_by = "";
      }
      modifiedByDisplay.textContent = value.modified_by;

      tileWrapper.appendChild(fieldContainer);
      tileWrapper.appendChild(modifiedByDisplay);
      categoryContainer.appendChild(tileWrapper);
    });
    container.appendChild(categoryContainer);
  });

  if (
    typeof patientData.intra_or_post_procedure_events.events.verified !==
      "object" ||
    patientData.intra_or_post_procedure_events.events.verified === null
  ) {
    patientData.intra_or_post_procedure_events.events.verified = {
      value:
        patientData.intra_or_post_procedure_events.events.verified || "False",
      modified_by: "",
    };
  }
  const verifiedData =
    patientData.intra_or_post_procedure_events.events.verified;
  const verifiedContainer = document.createElement("div");
  verifiedContainer.style.position = "absolute";
  verifiedContainer.style.bottom = "16px";
  verifiedContainer.style.right = "16px";
  verifiedContainer.style.display = "flex";
  verifiedContainer.style.alignItems = "center";

  const containerVerifiedCheckbox = document.createElement("input");
  containerVerifiedCheckbox.type = "checkbox";
  containerVerifiedCheckbox.id = "intraPostProcedureEvents-verified-checkbox";
  containerVerifiedCheckbox.checked = verifiedData.value === "True";
  containerVerifiedCheckbox.style.width = "24px";
  containerVerifiedCheckbox.style.height = "24px";
  containerVerifiedCheckbox.addEventListener("change", (event) => {
    verifiedData.value = event.target.checked ? "True" : "False";
    verifiedData.modified_by = "ABSTRACTOR";
    updateContainerHighlight(
      container,
      patientData.intra_or_post_procedure_events
    );
  });

  const containerVerifiedLabel = document.createElement("label");
  containerVerifiedLabel.setAttribute(
    "for",
    "intraPostProcedureEvents-verified-checkbox"
  );
  containerVerifiedLabel.classList.add("mt-2", "ml-2");
  containerVerifiedLabel.innerText = "Verified";
  containerVerifiedLabel.style.fontSize = "18px";
  containerVerifiedLabel.style.fontWeight = "bold";

  verifiedContainer.appendChild(containerVerifiedCheckbox);
  verifiedContainer.appendChild(containerVerifiedLabel);
  container.appendChild(verifiedContainer);

  updateContainerHighlight(
    container,
    patientData.intra_or_post_procedure_events
  );
}
